"""Mo<PERSON>les defining a Nanotec motion command.

This is an implementation of the abstract MotionCommand for a motor connected to
a Nanotec driver.

To handle the different kind of motors that we might use in the machines, an
abstract class is defined. This allows to have a template and to benefit from
OOP advantages for the motors and motions.
"""
from alibrary.motions.abstract.command import MotionCommand, MotionType


class NanotecMotionCommand(MotionCommand):
    """Implementation of the MotionCommand class for a motor connected to a
    Nanotec driver.
    """

    @classmethod
    def from_json(cls, json: dict[str,]) -> "NanotecMotionCommand":
        """Returns a NanotecMotionCommand from the given JSOn object.

        Args:
            json: A JSON object to deserialize

        Returns:
            A NanotecMotionCommand
        """
        motion_type = MotionType[str(
            json["mode"]).upper()] if "mode" in json else MotionType.RELATIVE

        speed = float(json["speed"]) if "speed" in json else 0.0

        distance = float(json["distance"]) if "distance" in json else 0.0

        turns = float(json["turns"]) if "turns" in json else 0.0

        return cls(motion_type=motion_type,
                   speed=speed,
                   distance=distance,
                   turns=turns)

    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this command.

        Returns:
            A JSON object representing this NanotecMotionCommand
        """
        json = {
            "mode": self.motion_type.name.lower(),
            "speed": self.speed,
            "distance": self.distance,
            "turns": self.turns
        }

        return json
