# Recoater System Reverse Engineering Analysis

## Overview

This document presents the findings from reverse engineering the recoater system codebase found in the `DOC` directory. The analysis reveals **two distinct control systems** for the 3-drum recoater: a traditional Controllino PLC-based system and a modern REST API-based system that provides direct hardware access.

## Executive Summary

### Key Findings
1. **Dual Control Architecture**: The system supports both Controllino PLC control and direct API-based control
2. **Existing API Infrastructure**: A complete REST API server already exists for external control
3. **Direct Hardware Access**: The API system bypasses Controllino for motor control via direct Modbus TCP
4. **Feasible Bypass Implementation**: Direct IPC-to-recoater control is technically feasible using existing protocols

### System Architecture Overview

The recoater system consists of two parallel control paths:

#### Path 1: Traditional Controllino-Based Control
- **Network**: 10.10.192.x infrastructure
- **Control Hub**: Controllino PLCs (Main: ***********, Secondary: ***********)
- **Motor Control**: Via Controllino → Nanotec drivers
- **Drum Control**: Via Controllino I/O systems

#### Path 2: Modern API-Based Control
- **API Server**: Flask + Connexion REST API (127.0.0.1:8080)
- **Direct Motor Control**: API → Nanotec drivers via Modbus TCP
- **Hardware Abstraction**: Alibrary framework for unified control
- **OpenAPI Specification**: Comprehensive API documentation (v3.3.0)

## Detailed System Architecture

### Primary Components

1. **Controllino PLC System**
   - **Main Controllino**: ***********:4080 - Primary control and safety systems
   - **Secondary Controllino**: ***********:4080 - Auxiliary control and collectors
   - **Communication**: TCP socket-based protocol
   - **Functions**: Pressure control, valve management, safety interlocks, lighting

2. **3-Drum Recoater Hardware**
   - **3 Powder Drums**: Independent powder reservoirs with individual control
   - **384 Valves per Drum**: Precise powder dispensing control
   - **Ejection Pressure System**: Pneumatic pressure control for powder flow
   - **Bridge Breakers & Collectors**: Powder management and cleanup systems
   - **Piezo Actuators**: Frequency-controlled vibration for powder flow

3. **Motion Control System**
   - **X-Axis Nanotec Driver**: ************:502 (Modbus TCP)
   - **Z-Axis Nanotec Driver**: ************:502 (Modbus TCP)
   - **X-Axis Motor**: Linear motion for recoater positioning (0-800mm range)
   - **Z-Axis Motor**: Vertical motion with gripper control (0-120mm range)
   - **Motion Types**: Absolute, relative, and homing operations

4. **REST API Server Infrastructure**
   - **Flask + Connexion**: Modern Python web framework with OpenAPI integration
   - **Alibrary Framework**: Custom hardware abstraction layer
   - **Direct Protocol Access**: Bypasses Controllino for critical operations
   - **Comprehensive API**: Full system control via HTTP REST endpoints

## System Architecture Diagrams

### Current Controllino-Based System
![Current System](../../docs/diagrams/current-controllino-system.md)

### Existing API-Based Control System
![API System](../../docs/diagrams/api-based-system.md)

### Proposed Direct IPC Control Implementation
![Proposed System](../../docs/diagrams/proposed-direct-control.md)

## Evidence of Dual Control Systems

The codebase contains comprehensive evidence of two distinct control systems operating in parallel:

### 1. Traditional Controllino PLC Control System

**Evidence from `code-test-python/controllino.py`:**
- Direct TCP socket communication with Controllino PLCs
- Hardware addresses: *********** (main) and *********** (secondary)
- Port 4080 for PLC communication
- Functions for pressure control, valve testing, safety monitoring
- Direct I/O control for drums, collectors, and safety systems

**Evidence from `code-test-python/motor_driver.py`:**
- Modbus TCP communication with Nanotec motor drivers
- Direct register access for motor control (position, speed, acceleration)
- Hardware addresses: ************ (X-axis), ************ (Z-axis)
- Comprehensive motor state management and error handling

### 2. Modern REST API Control System

The codebase contains strong evidence that the recoater system is designed to be controlled by a separate control system via a comprehensive REST API:

#### A. OpenAPI Specification (`openapi.yaml`)

The project includes a comprehensive OpenAPI 3.0 specification that defines the "Aerosint API v3.3.0" for controlling the recoater system:

**Motion Control Endpoints:**
- `GET/POST/DELETE /x/motion` - Complete X-axis control (absolute, relative, homing)
- `GET/POST/DELETE /z/motion` - Complete Z-axis control with safety interlocks
- `GET/PUT /z/gripper` - Z-axis gripper state management

**Drum Management Endpoints:**
- `PUT/DELETE /geometries/{drum_id}` - Drum geometry setup (PNG/CLI format)
- Support for binary data upload (application/octet-stream)

**Print Job Management:**
- `POST/DELETE /print/job` - Start/cancel print operations
- `GET /print/info` - Print job status and layer information
- `GET /print/parameters` - Comprehensive print parameter management
- `PUT /print/parameters` - Dynamic parameter updates during operation

**System Monitoring:**
- `GET /state` - Overall system state (ready, printing, waiting_for_reset, error)
- `GET /preview` - Layer preview images (PNG format)
- `GET/PUT /light` - Chamber lighting control

#### B. Complete API Server Implementation

**Flask + Connexion Architecture (`app.py`, `__main__.py`):**
- Production-ready REST API server using Flask and Connexion
- OpenAPI-driven development with automatic validation
- Default binding to `127.0.0.1:8080` (configurable for external access)
- Gunicorn WSGI server for production deployment

**Comprehensive Controller Layer (`controllers.py`):**
- Complete implementation of all OpenAPI endpoints
- Direct hardware abstraction via Alibrary framework
- Safety interlock integration (EMS, door, cover monitoring)
- Error handling with proper HTTP status codes

#### C. Hardware Abstraction Layer (`models.py`)

**Alibrary Framework Integration:**
- `alibrary.electronics.Controllino` - Direct Controllino PLC interface
- `alibrary.motions.nanotec.NanotecMotor` - Direct Nanotec motor control
- `alibrary.axis.XAxis`, `alibrary.axis.ZAxis` - High-level axis abstractions
- `alibrary.printer.Printer` - 3-drum system management
- `alibrary.recoater.executor.ProcedureExecutor` - Print job orchestration

**Direct Hardware Communication:**
- **Nanotec Drivers**: Direct Modbus TCP communication bypassing Controllino
- **Controllino PLCs**: Direct TCP socket communication for drum control
- **Safety Systems**: Real-time monitoring of EMS, door, and cover interlocks

#### D. Internal API Usage (`tasks.py`)

The system demonstrates self-consumption of its own API during print operations:

```python
base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"

# Dynamic drum geometry updates
for d in range(config.N_DRUMS):
    layer = m.printer.get_layer_for_drum(crt_layer, d)
    requests.put(
        f"{base_url}/geometries/{d}",
        data=layer,
        headers={"Content-Type": "application/octet-stream"},
        timeout=10.0)

# Print job management
requests.post(f"{base_url}/print/job", timeout=3.0)
```

#### E. Network Configuration (`config.py`)

**API Server Configuration:**
- `ASERVER_ADDRESS = "127.0.0.1:8080"` - Configurable for external access
- **Hardware IP Addresses:**
  - Controllino: `***********:4080`
  - X-Axis Nanotec: `************:502`
  - Z-Axis Nanotec: `************:502`

#### F. Web Interface Evidence

The `web` directory contains Flutter-based web application files, indicating an existing web-based control interface that likely connects to the REST API.

### 6. Model Initialization

The `models.py` file shows how the system initializes its components and makes API calls to set default parameters:

```python
# Set recoater default parameters
json = {"speed": c.PATTERNING_SPEED, "x_offset": c.X_OFFSET, "filling_id": -1}
requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/layer/parameters",
             json=json,
             timeout=3.0)

# Set recoater default config
response = requests.get(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
                        timeout=3.0)
json = response.json()
json["build_space_diameter"] = c.DEFAULT_DIE_DIAMETER

if "build_space_dimensions" in json:
    json.pop("build_space_dimensions")

requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
             json=json,
             timeout=3.0)
```

## API Endpoints

The API provides the following key endpoints:

### Axis Control
- GET/PUT `/x/motion` - Get/set X-axis motion
- GET/PUT `/z/motion` - Get/set Z-axis motion
- DELETE `/x/motion` - Cancel X-axis motion
- DELETE `/z/motion` - Cancel Z-axis motion

### Gripper Control
- GET/PUT `/z/gripper` - Get/set Z-axis gripper state

### Drum Control
- PUT `/drums/{drum_id}/geometry` - Set drum geometry
- DELETE `/drums/{drum_id}/geometry` - Delete drum geometry

### Print Job Management
- POST `/print/job` - Start a print job
- DELETE `/print/job` - Cancel a print job
- GET `/print/info` - Get print job information

### System State
- GET `/state` - Get the overall printer state (ready, printing, waiting_for_reset, error)

### Print Parameters
- GET/PUT `/print/parameters` - Get/set print parameters (speed, layer thickness, etc.)

## Implementing a Client to Call the REST API

To create a client that can interact with the recoater system's REST API, we need to implement the following components:

### 1. Client Implementation Requirements

1. **HTTP Client Library**
   - Python's `requests` library would be ideal as it's already used in the system
   - Alternatively, any HTTP client library in your preferred language (e.g., Axios for JavaScript, HttpClient for C#)

2. **API Base URL Configuration**
   - The API is served at `http://{server_address}/api/v3`
   - By default, the server address is `127.0.0.1:8080` (localhost)
   - For external access, this would need to be the IP address of the recoater system

3. **Authentication**
   - The current API implementation doesn't show any authentication mechanism
   - However, for production use, authentication should be considered

4. **Error Handling**
   - The API returns standard HTTP status codes
   - Error responses include a JSON body with `status_code` and `message` fields

### 2. Example Client Implementation (Python)

```python
import requests
import json

class RecoaterClient:
    def __init__(self, server_address="127.0.0.1:8080"):
        self.base_url = f"http://{server_address}/api/v3"
        self.timeout = 10.0  # Default timeout in seconds
    
    # Axis Control
    def get_x_info(self):
        response = requests.get(f"{self.base_url}/x", timeout=self.timeout)
        return response.json()
    
    def set_x_motion(self, mode, speed, distance=None):
        payload = {
            "mode": mode,  # "absolute", "relative", or "homing"
            "speed": speed
        }
        if distance is not None:
            payload["distance"] = distance
            
        response = requests.post(f"{self.base_url}/x/motion", json=payload, timeout=self.timeout)
        return response.status_code == 201
    
    def cancel_x_motion(self):
        response = requests.delete(f"{self.base_url}/x/motion", timeout=self.timeout)
        return response.status_code == 204
    
    # Z-Axis and Gripper Control
    def get_z_info(self):
        response = requests.get(f"{self.base_url}/z", timeout=self.timeout)
        return response.json()
    
    def set_z_motion(self, mode, speed, distance=None):
        payload = {
            "mode": mode,
            "speed": speed
        }
        if distance is not None:
            payload["distance"] = distance
            
        response = requests.post(f"{self.base_url}/z/motion", json=payload, timeout=self.timeout)
        return response.status_code == 201
    
    def set_gripper_state(self, state):
        payload = {"state": state}  # Boolean: True to close, False to open
        response = requests.put(f"{self.base_url}/z/gripper", json=payload, timeout=self.timeout)
        return response.status_code == 204
    
    # Drum Control
    def set_drum_geometry(self, drum_id, geometry_data):
        # geometry_data should be binary data (PNG or CLI format)
        response = requests.put(
            f"{self.base_url}/geometries/{drum_id}",
            data=geometry_data,
            headers={"Content-Type": "application/octet-stream"},
            timeout=self.timeout
        )
        return response.status_code == 204
    
    def delete_drum_geometry(self, drum_id):
        response = requests.delete(f"{self.base_url}/geometries/{drum_id}", timeout=self.timeout)
        return response.status_code == 204
    
    # Print Job Management
    def start_print_job(self):
        response = requests.post(f"{self.base_url}/print/job", timeout=self.timeout)
        return response.status_code == 202
    
    def cancel_print_job(self):
        response = requests.delete(f"{self.base_url}/print/job", timeout=self.timeout)
        return response.status_code == 204
    
    def get_print_info(self):
        response = requests.get(f"{self.base_url}/print/info", timeout=self.timeout)
        return response.json()
    
    # System State
    def get_state(self):
        response = requests.get(f"{self.base_url}/state", timeout=self.timeout)
        return response.json()
    
    # Print Parameters
    def get_print_parameters(self):
        response = requests.get(f"{self.base_url}/print/parameters", timeout=self.timeout)
        return response.json()
    
    def set_print_parameters(self, parameters):
        # parameters should be a dictionary with the appropriate keys
        response = requests.put(f"{self.base_url}/print/parameters", json=parameters, timeout=self.timeout)
        return response.status_code == 204
    
    # Layer Preview
    def get_layer_preview(self, layer_id=0):
        response = requests.get(
            f"{self.base_url}/preview",
            params={"layer_id": layer_id},
            headers={"Accept": "image/png"},
            timeout=self.timeout
        )
        if response.status_code == 200:
            return response.content  # Binary PNG data
        return None
```

### 3. Usage Example

```python
# Create a client instance
client = RecoaterClient("127.0.0.1:8080")

# Check system state
state = client.get_state()
print(f"System state: {state['state']}")

# Home the X-axis
client.set_x_motion(mode="homing", speed=15)

# Set print parameters
parameters = {
    "patterning_speed": 30,
    "travel_speed": 100,
    "z_speed": 5,
    "z_offset": 2,
    "layer_thickness": 0.08,
    "filling_id": 1
}
client.set_print_parameters(parameters)

# Start a print job
client.start_print_job()
```

## Considerations for Implementation

### 1. Error Handling

The client should implement robust error handling to deal with various scenarios:

- Network connectivity issues
- API server not responding
- Error responses from the API (4xx and 5xx status codes)
- Timeout handling

### 2. Connection Management

For frequent API calls, consider using a session to maintain a persistent connection:

```python
self.session = requests.Session()
# Then use self.session.get(), self.session.post(), etc.
```

### 3. Asynchronous Operations

For better performance, especially in GUI applications, consider implementing asynchronous API calls:

- In Python, use `asyncio` with `aiohttp`
- In JavaScript, use Promises or async/await
- In C#, use Task-based Asynchronous Pattern (TAP)

### 4. Security Considerations

If deploying in a production environment:

- Implement proper authentication (e.g., API keys, OAuth)
- Use HTTPS instead of HTTP
- Implement rate limiting to prevent abuse
- Consider implementing a proxy server for additional security

### 5. Testing

Before connecting to the actual recoater system:

- Create mock responses to test client functionality
- Implement unit tests for each API endpoint
- Test error handling with simulated failures

## Feasibility Assessment for Direct IPC-to-Recoater Control

### Technical Feasibility: **HIGH** ✅

Based on the comprehensive analysis, implementing direct IPC-to-recoater control via USB3-to-RJ45 adapter is **highly feasible** for the following reasons:

#### 1. Existing Protocol Documentation
- **Complete OpenAPI Specification**: The system already has a well-documented REST API
- **Known Hardware Protocols**: Modbus TCP for motors, TCP sockets for Controllino
- **Reference Implementation**: The existing API server provides a complete reference

#### 2. Hardware Compatibility
- **Standard Protocols**: All hardware uses standard Ethernet-based communication
- **Direct Access Proven**: The API system already bypasses Controllino for motor control
- **Simple Network Setup**: Point-to-point connection via USB3-to-RJ45 adapter

#### 3. Software Complexity Assessment

**For a Beginner Programmer:**

**EASY (Recommended Starting Point):**
- **Option 1**: Use existing REST API client approach
- **Skill Level**: Basic HTTP/REST knowledge
- **Time Estimate**: 2-4 weeks for basic functionality
- **Risk Level**: LOW - Well-documented, proven approach

**MODERATE:**
- **Option 2**: Direct Modbus TCP for motor control only
- **Skill Level**: Understanding of Modbus protocol
- **Time Estimate**: 4-8 weeks for motor control
- **Risk Level**: MEDIUM - Requires protocol knowledge

**ADVANCED:**
- **Option 3**: Complete hardware reverse engineering
- **Skill Level**: Deep embedded systems knowledge
- **Time Estimate**: 3-6 months for full system
- **Risk Level**: HIGH - Requires extensive reverse engineering

### Recommended Implementation Strategy

#### Phase 1: Proof of Concept (2-3 weeks)
1. **Setup Direct Network Connection**
   - Configure USB3-to-RJ45 adapter
   - Assign static IP addresses to motor drivers
   - Test basic connectivity

2. **Implement Basic Motor Control**
   - Use existing Python Modbus libraries (pymodbus)
   - Implement simple X/Z axis movements
   - Test homing and positioning operations

3. **Validate Communication**
   - Verify motor response and feedback
   - Test safety interlock monitoring
   - Confirm timing requirements

#### Phase 2: API Client Development (3-4 weeks)
1. **Reverse Engineer API Protocol**
   - Study existing controllers.py implementation
   - Understand request/response formats
   - Map API endpoints to hardware functions

2. **Build Custom API Client**
   - Implement HTTP client using requests library
   - Create abstraction layer for hardware control
   - Add error handling and retry logic

3. **Integrate Drum Control**
   - Understand Controllino communication protocol
   - Implement drum geometry and pressure control
   - Test 3-drum coordination

#### Phase 3: Safety and Production (2-3 weeks)
1. **Implement Safety Systems**
   - Software-based emergency stop
   - Interlock monitoring and response
   - Fault detection and recovery

2. **User Interface Development**
   - Web-based control interface (recommended)
   - Real-time status monitoring
   - Parameter configuration and tuning

3. **Testing and Validation**
   - Comprehensive system testing
   - Performance optimization
   - Documentation and user guides

### Technical Challenges and Solutions

#### Challenge 1: Safety System Implementation
**Problem**: Controllino handles critical safety functions
**Solution**:
- Implement software-based safety monitoring
- Use redundant safety checks in software
- Consider hardware emergency stop as backup

#### Challenge 2: Real-time Control Requirements
**Problem**: Precise timing for powder dispensing
**Solution**:
- Use dedicated threads for time-critical operations
- Implement proper buffering and queuing
- Consider real-time operating system if needed

#### Challenge 3: Protocol Reverse Engineering
**Problem**: Understanding proprietary communication protocols
**Solution**:
- Start with well-documented Modbus TCP for motors
- Use existing API implementation as reference
- Implement incremental functionality testing

#### Challenge 4: Hardware Interface Compatibility
**Problem**: Ensuring direct hardware communication works
**Solution**:
- Test with existing network infrastructure first
- Validate USB3-to-RJ45 adapter compatibility
- Have fallback to existing network if needed

### Cost-Benefit Analysis

#### Costs
- **Development Time**: 8-12 weeks for complete implementation
- **Hardware**: ~$50 for USB3-to-RJ45 adapter
- **Learning Curve**: Moderate for networking and protocols

#### Benefits
- **Independence**: No dependency on Controllino PLC
- **Flexibility**: Complete control over software implementation
- **Cost Savings**: Eliminates need for expensive PLC hardware
- **Customization**: Tailored interface for specific requirements
- **Scalability**: Easy to extend and modify functionality

### Conclusion and Recommendation

**RECOMMENDATION: PROCEED WITH IMPLEMENTATION** ✅

The analysis strongly supports the feasibility of implementing direct IPC-to-recoater control. The combination of:
- Existing API documentation and reference implementation
- Standard communication protocols (Modbus TCP, HTTP REST)
- Proven hardware compatibility
- Manageable complexity for a beginner programmer

Makes this project **highly likely to succeed** with the recommended phased approach.

**Next Steps:**
1. Acquire USB3-to-RJ45 adapter and test basic connectivity
2. Set up development environment with Python and required libraries
3. Begin Phase 1 implementation with motor control proof of concept
4. Use existing codebase as reference throughout development

The existing REST API infrastructure provides an excellent foundation, and the direct hardware access patterns are already proven in the current system. This significantly reduces the technical risk and development complexity.