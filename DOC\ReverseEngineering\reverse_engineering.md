# Recoater System Reverse Engineering Analysis

## Overview

This document presents the findings from reverse engineering the recoater system codebase found in the `DOC` directory. The analysis focuses on understanding the system architecture, particularly how the recoater system is controlled and whether it supports external control via API interfaces.

## System Architecture

### Primary Components

1. **Controllino PLC**
   - Manages communication with hardware via sockets
   - Controls pressure, frequency, and various components
   - Supports multiple drums (specifically 3 drums/barrels)

2. **Recoater System**
   - Contains multiple drums (3) for powder deposition
   - Includes bridge breakers, shovels, and other mechanical components
   - Manages ejection pressure, suction, and other parameters for each drum

3. **Motion Control**
   - X-Axis and Z-Axis control via Nanotec motor drivers
   - Gripper control for Z-Axis
   - Support for various motion types (absolute, relative, homing)

4. **REST API Server**
   - Provides external control interface
   - Implemented using Flask and Connexion
   - Defined using OpenAPI specification

## Evidence of REST API Interface

The codebase contains strong evidence that the recoater system is designed to be controlled by a separate control system via a REST API:

### 1. OpenAPI Specification

The project includes an `openapi.yaml` file that defines a comprehensive API for controlling the recoater system. This API is explicitly named "Aerosint API" and provides endpoints for:

- Controlling X and Z axis movements
- Managing the Z-axis gripper
- Setting and retrieving drum geometries
- Managing print jobs (starting, canceling, getting status)
- Retrieving layer previews
- Getting the overall printer state
- Setting print parameters

### 2. API Server Implementation

The codebase contains a complete implementation of a REST API server:

- `app.py` defines a Flask application using Connexion to create a REST API server
- `__main__.py` implements a production server using Gunicorn that binds to `127.0.0.1:8080` by default
- `controllers.py` implements all the API endpoints defined in the OpenAPI specification

### 3. Client-Server Communication

The `tasks.py` file shows how the system makes HTTP requests to its own API endpoints during printing procedures:

```python
base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"

# Put drums geometries
for d in range(config.N_DRUMS):
    layer = m.printer.get_layer_for_drum(crt_layer, d)

    requests.put(
        f"{base_url}/drums/{d}/geometry",
        data=layer,
        headers={"Content-Type": "application/octet-stream"},
        timeout=10.0)

# Start print job
requests.post(f"{base_url}/print/job", timeout=3.0)
```

### 4. Configuration for External Access

The `config.py` file defines `ASERVER_ADDRESS = "127.0.0.1:8080"`, which is the address of the API server. This could be configured to allow external systems to connect to the API.

### 5. Web Interface Evidence

The presence of a `web` directory with Flutter-related files suggests that there might be a web-based interface that connects to this API to control the recoater system.

### 6. Model Initialization

The `models.py` file shows how the system initializes its components and makes API calls to set default parameters:

```python
# Set recoater default parameters
json = {"speed": c.PATTERNING_SPEED, "x_offset": c.X_OFFSET, "filling_id": -1}
requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/layer/parameters",
             json=json,
             timeout=3.0)

# Set recoater default config
response = requests.get(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
                        timeout=3.0)
json = response.json()
json["build_space_diameter"] = c.DEFAULT_DIE_DIAMETER

if "build_space_dimensions" in json:
    json.pop("build_space_dimensions")

requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
             json=json,
             timeout=3.0)
```

## API Endpoints

The API provides the following key endpoints:

### Axis Control
- GET/PUT `/x/motion` - Get/set X-axis motion
- GET/PUT `/z/motion` - Get/set Z-axis motion
- DELETE `/x/motion` - Cancel X-axis motion
- DELETE `/z/motion` - Cancel Z-axis motion

### Gripper Control
- GET/PUT `/z/gripper` - Get/set Z-axis gripper state

### Drum Control
- PUT `/drums/{drum_id}/geometry` - Set drum geometry
- DELETE `/drums/{drum_id}/geometry` - Delete drum geometry

### Print Job Management
- POST `/print/job` - Start a print job
- DELETE `/print/job` - Cancel a print job
- GET `/print/info` - Get print job information

### System State
- GET `/state` - Get the overall printer state (ready, printing, waiting_for_reset, error)

### Print Parameters
- GET/PUT `/print/parameters` - Get/set print parameters (speed, layer thickness, etc.)

## Implementing a Client to Call the REST API

To create a client that can interact with the recoater system's REST API, we need to implement the following components:

### 1. Client Implementation Requirements

1. **HTTP Client Library**
   - Python's `requests` library would be ideal as it's already used in the system
   - Alternatively, any HTTP client library in your preferred language (e.g., Axios for JavaScript, HttpClient for C#)

2. **API Base URL Configuration**
   - The API is served at `http://{server_address}/api/v3`
   - By default, the server address is `127.0.0.1:8080` (localhost)
   - For external access, this would need to be the IP address of the recoater system

3. **Authentication**
   - The current API implementation doesn't show any authentication mechanism
   - However, for production use, authentication should be considered

4. **Error Handling**
   - The API returns standard HTTP status codes
   - Error responses include a JSON body with `status_code` and `message` fields

### 2. Example Client Implementation (Python)

```python
import requests
import json

class RecoaterClient:
    def __init__(self, server_address="127.0.0.1:8080"):
        self.base_url = f"http://{server_address}/api/v3"
        self.timeout = 10.0  # Default timeout in seconds
    
    # Axis Control
    def get_x_info(self):
        response = requests.get(f"{self.base_url}/x", timeout=self.timeout)
        return response.json()
    
    def set_x_motion(self, mode, speed, distance=None):
        payload = {
            "mode": mode,  # "absolute", "relative", or "homing"
            "speed": speed
        }
        if distance is not None:
            payload["distance"] = distance
            
        response = requests.post(f"{self.base_url}/x/motion", json=payload, timeout=self.timeout)
        return response.status_code == 201
    
    def cancel_x_motion(self):
        response = requests.delete(f"{self.base_url}/x/motion", timeout=self.timeout)
        return response.status_code == 204
    
    # Z-Axis and Gripper Control
    def get_z_info(self):
        response = requests.get(f"{self.base_url}/z", timeout=self.timeout)
        return response.json()
    
    def set_z_motion(self, mode, speed, distance=None):
        payload = {
            "mode": mode,
            "speed": speed
        }
        if distance is not None:
            payload["distance"] = distance
            
        response = requests.post(f"{self.base_url}/z/motion", json=payload, timeout=self.timeout)
        return response.status_code == 201
    
    def set_gripper_state(self, state):
        payload = {"state": state}  # Boolean: True to close, False to open
        response = requests.put(f"{self.base_url}/z/gripper", json=payload, timeout=self.timeout)
        return response.status_code == 204
    
    # Drum Control
    def set_drum_geometry(self, drum_id, geometry_data):
        # geometry_data should be binary data (PNG or CLI format)
        response = requests.put(
            f"{self.base_url}/geometries/{drum_id}",
            data=geometry_data,
            headers={"Content-Type": "application/octet-stream"},
            timeout=self.timeout
        )
        return response.status_code == 204
    
    def delete_drum_geometry(self, drum_id):
        response = requests.delete(f"{self.base_url}/geometries/{drum_id}", timeout=self.timeout)
        return response.status_code == 204
    
    # Print Job Management
    def start_print_job(self):
        response = requests.post(f"{self.base_url}/print/job", timeout=self.timeout)
        return response.status_code == 202
    
    def cancel_print_job(self):
        response = requests.delete(f"{self.base_url}/print/job", timeout=self.timeout)
        return response.status_code == 204
    
    def get_print_info(self):
        response = requests.get(f"{self.base_url}/print/info", timeout=self.timeout)
        return response.json()
    
    # System State
    def get_state(self):
        response = requests.get(f"{self.base_url}/state", timeout=self.timeout)
        return response.json()
    
    # Print Parameters
    def get_print_parameters(self):
        response = requests.get(f"{self.base_url}/print/parameters", timeout=self.timeout)
        return response.json()
    
    def set_print_parameters(self, parameters):
        # parameters should be a dictionary with the appropriate keys
        response = requests.put(f"{self.base_url}/print/parameters", json=parameters, timeout=self.timeout)
        return response.status_code == 204
    
    # Layer Preview
    def get_layer_preview(self, layer_id=0):
        response = requests.get(
            f"{self.base_url}/preview",
            params={"layer_id": layer_id},
            headers={"Accept": "image/png"},
            timeout=self.timeout
        )
        if response.status_code == 200:
            return response.content  # Binary PNG data
        return None
```

### 3. Usage Example

```python
# Create a client instance
client = RecoaterClient("127.0.0.1:8080")

# Check system state
state = client.get_state()
print(f"System state: {state['state']}")

# Home the X-axis
client.set_x_motion(mode="homing", speed=15)

# Set print parameters
parameters = {
    "patterning_speed": 30,
    "travel_speed": 100,
    "z_speed": 5,
    "z_offset": 2,
    "layer_thickness": 0.08,
    "filling_id": 1
}
client.set_print_parameters(parameters)

# Start a print job
client.start_print_job()
```

## Considerations for Implementation

### 1. Error Handling

The client should implement robust error handling to deal with various scenarios:

- Network connectivity issues
- API server not responding
- Error responses from the API (4xx and 5xx status codes)
- Timeout handling

### 2. Connection Management

For frequent API calls, consider using a session to maintain a persistent connection:

```python
self.session = requests.Session()
# Then use self.session.get(), self.session.post(), etc.
```

### 3. Asynchronous Operations

For better performance, especially in GUI applications, consider implementing asynchronous API calls:

- In Python, use `asyncio` with `aiohttp`
- In JavaScript, use Promises or async/await
- In C#, use Task-based Asynchronous Pattern (TAP)

### 4. Security Considerations

If deploying in a production environment:

- Implement proper authentication (e.g., API keys, OAuth)
- Use HTTPS instead of HTTP
- Implement rate limiting to prevent abuse
- Consider implementing a proxy server for additional security

### 5. Testing

Before connecting to the actual recoater system:

- Create mock responses to test client functionality
- Implement unit tests for each API endpoint
- Test error handling with simulated failures

## Conclusion

The evidence strongly indicates that the recoater system is designed with a REST API interface that allows a separate control system to connect to and control it. The API is well-defined using OpenAPI specifications and is implemented using Flask and Connexion.

Implementing a client to interact with this API is straightforward using standard HTTP client libraries. The example Python implementation provided above demonstrates how to interact with all the key endpoints of the API.

By following the considerations outlined in this document, it should be possible to create a robust client application that can control all aspects of the recoater system remotely, whether for integration with other systems, automation, or creating custom user interfaces.