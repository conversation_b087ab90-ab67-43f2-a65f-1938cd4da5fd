"""Module defining an interface to the pressure sensors and steppers PCB.
"""
from math import ceil
import socket
from multiprocessing import Lock
import struct

from alibrary.electronics.ethernet import <PERSON>ther<PERSON>Component
from alibrary.logger import logger


class PssPCBError(Exception):
    """Exception raised when an error occurs in the communication with the PCB.
    """


class PssPCB(EthernetComponent):
    """An interface to the pressure sensors and steppers PCB.
    """

    def __init__(
        self,
        n_sensors: int,
        ip: str,
        port: int,
        timeout: int = 2,
        offline: bool = False,
    ) -> None:
        super().__init__(ip, port, timeout, offline)

        self.n_sensors = n_sensors
        self.lock = Lock()

        self._cache = [0 for _ in range(self.n_sensors)]
        self._cache_timestamp = 0

        if not self.offline:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            try:
                self.socket.connect((self.ip, self.port))

            except socket.timeout as error:
                logger.error("(PssPCB) Connection timeout while reading (%s)",
                             error)
                raise PssPCBError(str(error)) from error
            except socket.error as error:
                logger.error("(PssPCB) Error while reading (%s)", error)
                raise PssPCBError(str(error)) from error

    def __read(self, n_bytes: int = 1, signed: bool = False) -> int:
        """Reads and returns an integer from the PCB.

        It reads `n_bytes` bytes and then converts them into an integer.

        Args:
            n_bytes: The number of bytes to read
            signed: A flag indicating if the two's complement should be used in
            the conversion
        """
        assert self.offline is False

        data = b""
        for _ in range(n_bytes):
            data += self.socket.recv(1)

        return int.from_bytes(data, byteorder="big", signed=signed)

    def __send(self, value: int, n_bytes: int = 1, signed: bool = False):
        """Sends the given integer to the PCB.

        It converts the given int to bytes using the given number of bytes and
        the signed flag. It then sends all bytes to the PCB through a socket.

        Args:
            value: The integer to send
            n_bytes: The number of bytes the integer should be converted to
            signed: A flag indicating if the two's complement should be used in
            the conversion

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        assert self.offline is False

        data = value.to_bytes(n_bytes, byteorder="big", signed=signed)
        self.socket.sendall(data)

    def __send_float(self, value):
        data = bytearray(struct.pack(">f", value))
        for i in range(4):
            self.__send(data[i])

    def __read_float(self):
        data = b""
        for _ in range(4):
            data += self.socket.recv(1)

        return struct.unpack(">f", data)[0]

    def set_config(self, configuration_number):
        """ Sets config"""
        if not self.offline:
            if configuration_number == 0:  # SPD2: 2 meshes
                nb_2130 = 9
                chip_select_number_2130 = [5, 4, 3, 2, 9, 0xA1, 0xA2, 0xA3, 0xA7]
                mux_configuration_2130 = [False] * nb_2130
                chip_select_number_429 = [8, 7, 6]
                mux_configuration_429 = [False] * 3
                IS_VALVE = [
                    False, False, True, True, False, False, True, True, False
                ]
                nb_control = 3
                min_index_sensor = 0
                max_index_sensor = 5
                index_control_stepper = [2, 6, 7]
                index_control_sensor = [2, 4, 5]

            if configuration_number == 1:  # SPD2: 3 meshes configuration
                nb_2130 = 15
                chip_select_number_2130 = [
                    1, 2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 15, 3, 0xA1, 0xA2
                ]
                mux_configuration_2130 = [True] * 12 + [False] * 3
                chip_select_number_429 = [0, 4, 8, 12, 4]
                mux_configuration_429 = [True] * 4 + [False]
                IS_VALVE = [
                    False, False, True, True, True, False, False, True, False,
                    False, True, False, False, False, False
                ]
                nb_control = 4
                min_index_sensor = 0
                max_index_sensor = 7
                index_control_stepper = [3, 4, 7, 10]
                index_control_sensor = [2, 4, 6, 7]

            elif configuration_number == 2:  # Micro configuration

                nb_2130 = 5
                chip_select_number_2130 = [5, 4, 3, 2, 9]
                mux_configuration_2130 = [False] * 5
                chip_select_number_429 = [8, 7]
                mux_configuration_429 = [False] * 2
                IS_VALVE = [True] * 5
                nb_control = 4
                min_index_sensor = 2
                max_index_sensor = 11
                index_control_stepper = [1, 2, 3, 4]
                index_control_sensor = [3, 5, 7, 9]

            self.__send(0)
            if self.__read() == 1:
                print("Arduino already configured")
                return

            NB_STEPPERS_PER_TMC429 = 3
            nb_tmc429 = ceil(nb_2130 / NB_STEPPERS_PER_TMC429)

            self.__send(nb_2130)
            data_mux_configuration_2130 = 0
            for i in range(nb_2130):
                if mux_configuration_2130[i]:
                    data_mux_configuration_2130 += 2**i
            self.__send(data_mux_configuration_2130, 2)
            for i in range(nb_2130):
                self.__send(chip_select_number_2130[i])

            self.__send(nb_tmc429)
            data_mux_configuration_429 = 0
            for i in range(nb_tmc429):
                if mux_configuration_429[i]:
                    data_mux_configuration_429 += 2**i
            self.__send(data_mux_configuration_429)
            for i in range(nb_tmc429):
                self.__send(chip_select_number_429[i])

            send_data_is_valve = 0
            for i in range(nb_2130):
                if IS_VALVE[i]:
                    send_data_is_valve += 2**i
            self.__send(send_data_is_valve, 2)
            self.__send(min_index_sensor)
            self.__send(max_index_sensor)
            self.__send(nb_control)
            for i in range(nb_control):
                self.__send(index_control_stepper[i])
                self.__send(index_control_sensor[i])

            if self.check_driver_communication():
                print("no error communication with TMC2130 and TMC429")
            else:
                print(
                    "Error when during test communication with TMC2130 and TMC429")

    def get_raw_pressures(self) -> list[int]:
        """Returns all the measured pressures.

        Returns:
            A list of float representing the pressures

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        pressures = [0 for _ in range(self.n_sensors)]

        if not self.offline:
            with self.lock:
                self.__send(1)

                # Reading
                for i in range(self.n_sensors):
                    data = self.__read(2)
                    pressures[i] = data

        logger.debug("(PCB) Reading raw pressures %s", pressures)
        return pressures

    def perform_homing(self, index: int):
        """Performs the homing of the requested component.

        Args:
            index: An index selecting the component on which to perform the
            homing

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Performing homing of %s", bin(index))
        if not self.offline:
            with self.lock:
                self.__send(2)
                self.__send(index)

    def check_homing_done(self) -> int:
        """Checks on all component if the homing has been performed.

        It returns a binary number where a 1 at a given position means that
        the corresponding component has been homed.

        Example:
            0000 1001: The first and fourth components have been homed,
            the others not

        Returns:
            A binary number

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Checking homing")
        if not self.offline:
            with self.lock:
                self.__send(3)
                return self.__read(2)
        return 65535

    def perform_distance_motion(self, stepper_index: int, target: int):
        """Start an absolute distance motion on the specified stepper.

        Args:
            stepper_index: The index of the stepper to control
            target: The absolute target distance to reach

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Performing distance motion to %s on %s", target,
                     stepper_index)
        if not self.offline:
            with self.lock:
                self.__send(4)
                # self.__send(1)
                self.__send(stepper_index)
                self.__send(target, n_bytes=4, signed=True)

    def set_actual_position(self, stepper_index: int, position: int):
        """Sets the actual position of the specified stepper.

        To avoid too many homing on the scraping blades steppers, their current
        position is saved to a file and then restored at each startup. This
        methods allows to signified to a given stepper its actual position.

        Args:
            stepper_index: The index of the stepper to calibrate
            position: The position to set in the stepper

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Setting actual position to %s on stepper %s",
                     position, stepper_index)
        if not self.offline:
            with self.lock:
                self.__send(5)
                self.__send(stepper_index)
                self.__send(position, n_bytes=4, signed=True)

    def start_pressure_control(self, control_index: int, stepper_index: int, sensor_index:int, data: int):
        """Starts the pressure control to maintain the requested pressure in
        the given component.

        The index allows to choose the destination of this command.

        Args:
            control_index: The destination of this command
            data: The raw pressure to set

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Starting pressure control of %s for %s", data,
                     control_index)
        if not self.offline:
            with self.lock:
                self.__send(6)
                self.__send(control_index)
                self.__send(stepper_index)
                self.__send(sensor_index)
                self.__send(data, n_bytes=2)

    def stop_pressure_control(self, control_index: int):
        """Stops the pressure control of the given component

        Args:
            control_index: The destination of this command

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Stopping pressure control for %s", control_index)
        if not self.offline:
            with self.lock:
                self.__send(7)
                self.__send(control_index)

    def check_driver_communication(self):
        self.__send(8)
        communication_429 = self.__read()
        communication_2130 = self.__read(2)
        logger.warning(communication_429)
        logger.warning(communication_2130)

    def get_actual_position(self, stepper_index: int) -> int:
        """Returns the actual position of the specified stepper.

        Args:

        Returns:
        """
        if not self.offline:
            self.__send(9)
            self.__send(stepper_index)
            return self.__read(4, signed=True)
        return 0

    def check_busy(self) -> int:
        """Checks on all steppers if they are running or not.

        It returns a binary number where a 1 at a given position means that
        the corresponding stepper is busy.

        Example:
            0000 1001: The first and fourth steppers are busy, the others not

        Returns:
            A binary number

        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Checking busy")
        if not self.offline:
            with self.lock:
                self.__send(10)
                return self.__read(2)
        return 65535

    def set_feedback_gains(self, control_index, proportional_gain, integral_gain):
        """Sets the integral gain of the given controlled valve.

        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(11)
                self.__send(control_index)
                self.__send(proportional_gain, n_bytes=4, signed=True)
                self.__send(integral_gain, n_bytes=4, signed=True)

    # 250
    def set_rms_position(self, current):
        """Sets the RMS current when the valve makes a position motion.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(14)
                self.__send(current, n_bytes=2)

    # 200
    def set_rms_control(self, control_index, current):
        """Sets the RMS current when the given valve is regulating.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(15)
                self.__send(control_index)
                self.__send(current, n_bytes=2)

    # def set_regulating_valve(self, index):
    #     """Sets if the leveler pressure is regulated using the leveler valve or
    #     the hood valve.

    #     Args:

    #     """
    #     if not self.offline:
    #         with self.lock:
    #             self.__send(16)
    #             self.__send(index)

    def set_model_parameter(self, index, model_parameter):
        """Sets the model parameter of the control number index for adaptive
        feedback.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(17)
                self.__send(index)
                self.__send_float(model_parameter)

    def get_model_parameter(self, index):
        """Returns the model parameter of the control number index for adaptive
        feedback.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(18)
                self.__send(index)
                return self.__read_float()

    def enable_printing(self):
        """Enables printing mode.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(19)

    def disable_printing(self):
        """Disables printing mode.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(20)

    def enable_adaptive_feedback(self, control_id):
        """Enables printing mode.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(21)
                self.__send(control_id)

    def disable_adaptive_feedback(self, control_id):
        """Enables printing mode.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(22)
                self.__send(control_id)

    def set_coef(self, control_index, proportional_coef, integral_coef):
        """Set coefficients for adaptive feedback.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(23)
                self.__send(control_index)
                self.__send_float(proportional_coef)
                self.__send_float(integral_coef)

    def get_feedback_gains(self, control_index:int):
        """get feedback gains.

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(24)
                self.__send(control_index)
                return self.__read_float(), self.__read_float()
        return 0, 0

    def set_model_estimation_parameters(self, update_rate, period_update_mode,
                                        time_for_model_estimation, time_for_pressure_to_stabilize):
        """Set the parameters for model estimation..

        Args:

        """
        if not self.offline:
            with self.lock:
                self.__send(25)
                self.__send_float(update_rate)
                self.__send(period_update_mode, 4)
                self.__send(time_for_model_estimation, 4)
                self.__send(time_for_pressure_to_stabilize, 4)

    def set_sensor_data_deviation(self, control_number, pressure_deviation):
        """Set the sensor data deviation.

        Args:

        """
        data_deviation = self.compute_diff_data(pressure_deviation, 1034, 0)
        print(data_deviation)
        if not self.offline:
            with self.lock:
                self.__send(34)
                self.__send(control_number)
                self.__send(data_deviation, 4)

    def set_max_feedback_gains(self, control_id, max_kp, max_ki):
        if not self.offline:
            with self.lock:
                self.__send(35)
                self.__send(control_id)
                self.__send(max_kp, 4)
                self.__send(max_ki, 4)

    @staticmethod
    def compute_diff_data(delta_pressure: float, pmax:int, pmin:int):
        NMAX = 16384
        return int( ( (0.8 * NMAX) / (pmax - pmin) ) * (delta_pressure) )

    # def perform_distance_motion_new(self, stepper_index, position):
    #     """Send values and then check if the ones read are the same."""
    #     logger.warning(self.check_actual_position(stepper_index))
    #     if not self.offline:
    #         with self.lock:
    #             self.__send(19)
    #             self.__send(stepper_index)
    #             self.__send(position, n_bytes=4, signed=True)

    #             pcb_stepper_index = self.__read()
    #             pcb_position = self.__read(n_bytes=4, signed=True)

    #             if (pcb_stepper_index != stepper_index or
    #                     pcb_position != position):
    #                 logger.warning(
    #                     "(PCB) Error in communication. Send '%s' and '%s' but\
    #                         received '%s' and '%s'", stepper_index, position,
    #                     pcb_stepper_index, pcb_position)
    #             else:
    #                 logger.debug("Communication checked")
    #     logger.warning(self.check_actual_position(stepper_index))

    # FIXME: Duplicate method with get_actual_position
    # def check_actual_position(self, stepper_index):
    #     """Reads the current position."""
    #     if not self.offline:
    #         with self.lock:
    #             self.__send(20)
    #             self.__send(stepper_index)
    #             position = self.__read(n_bytes=4, signed=True)
    #             return position
    #     return 0.0
