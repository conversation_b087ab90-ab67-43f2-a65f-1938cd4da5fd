# Proposed Direct IPC-to-Recoater Control System

This diagram shows the proposed implementation for bypassing the Controllino controller and implementing direct IPC-to-recoater control via USB3-to-RJ45 adapter.

```mermaid
graph TB
    subgraph "IPC Control System"
        IPC[IPC/Host Computer<br/>Your Control Software]
        USB3[USB3 Port]
        ADAPTER[USB3-to-RJ45<br/>Ethernet Adapter]
    end
    
    subgraph "Direct Network Connection"
        DIRECT_NET[Direct Ethernet<br/>Point-to-Point]
    end
    
    subgraph "Bypassed Components"
        BYPASS_CTRL[❌ Controllino PLC<br/>BYPASSED]
        BYPASS_NET[❌ 10.10.192.x Network<br/>BYPASSED]
    end
    
    subgraph "Direct Hardware Access"
        NANO_X_DIRECT[Nanotec X Driver<br/>Direct IP Assignment<br/>Modbus TCP]
        NANO_Z_DIRECT[Nanotec Z Driver<br/>Direct IP Assignment<br/>Modbus TCP]
        RECOATER_DIRECT[3-Drum Recoater<br/>Direct Control Interface]
    end
    
    subgraph "Your Custom Software Stack"
        API_CLIENT[Custom API Client<br/>Python/C#/Web]
        MOTION_LIB[Motion Control Library<br/>Modbus TCP Client]
        RECOATER_LIB[Recoater Control Library<br/>Direct Hardware Interface]
        SAFETY_LIB[Safety Management<br/>Software-based Interlocks]
    end
    
    subgraph "Physical Hardware"
        MOTOR_X_PROP[X-Axis Motor<br/>Linear Motion]
        MOTOR_Z_PROP[Z-Axis Motor<br/>Vertical Motion]
        GRIPPER_PROP[Z-Axis Gripper]
        DRUM1_PROP[Drum 1 + Valves]
        DRUM2_PROP[Drum 2 + Valves]
        DRUM3_PROP[Drum 3 + Valves]
        PRESSURE_PROP[Pressure Control<br/>Direct Interface]
    end
    
    subgraph "Implementation Options"
        OPT1[Option 1: Reverse Engineer<br/>Existing API Protocol]
        OPT2[Option 2: Direct Modbus<br/>Motor Control Only]
        OPT3[Option 3: Hybrid Approach<br/>API + Direct Control]
    end
    
    %% Direct Connection Path
    IPC -->|USB3| USB3
    USB3 -->|USB3-to-Ethernet| ADAPTER
    ADAPTER -->|Ethernet Cable| DIRECT_NET
    
    %% Direct Hardware Communication
    DIRECT_NET -->|Direct TCP/IP| NANO_X_DIRECT
    DIRECT_NET -->|Direct TCP/IP| NANO_Z_DIRECT
    DIRECT_NET -->|Custom Protocol| RECOATER_DIRECT
    
    %% Software Stack
    IPC -->|Software Layer| API_CLIENT
    API_CLIENT -->|Motion Commands| MOTION_LIB
    API_CLIENT -->|Recoater Commands| RECOATER_LIB
    API_CLIENT -->|Safety Checks| SAFETY_LIB
    
    %% Hardware Control
    MOTION_LIB -->|Modbus TCP| NANO_X_DIRECT
    MOTION_LIB -->|Modbus TCP| NANO_Z_DIRECT
    RECOATER_LIB -->|Direct Interface| RECOATER_DIRECT
    
    %% Physical Hardware
    NANO_X_DIRECT -->|Control| MOTOR_X_PROP
    NANO_Z_DIRECT -->|Control| MOTOR_Z_PROP
    NANO_Z_DIRECT -->|Control| GRIPPER_PROP
    RECOATER_DIRECT -->|Control| DRUM1_PROP
    RECOATER_DIRECT -->|Control| DRUM2_PROP
    RECOATER_DIRECT -->|Control| DRUM3_PROP
    RECOATER_DIRECT -->|Control| PRESSURE_PROP
    
    %% Implementation Options
    API_CLIENT -.->|Choose| OPT1
    API_CLIENT -.->|Choose| OPT2
    API_CLIENT -.->|Choose| OPT3
    
    %% Bypass Indicators
    BYPASS_CTRL -.->|No longer needed| RECOATER_DIRECT
    BYPASS_NET -.->|Replaced by| DIRECT_NET
    
    %% Key Advantages
    subgraph "Advantages"
        ADV1[✓ Direct Control<br/>No PLC Dependency]
        ADV2[✓ Custom Software<br/>Full Flexibility]
        ADV3[✓ Simplified Network<br/>Point-to-Point]
        ADV4[✓ Cost Effective<br/>USB3-to-RJ45 Adapter]
    end
    
    %% Key Challenges
    subgraph "Challenges"
        CHAL1[⚠️ Safety Systems<br/>Software Implementation]
        CHAL2[⚠️ Protocol Reverse Engineering<br/>Recoater Communication]
        CHAL3[⚠️ Real-time Control<br/>Timing Requirements]
        CHAL4[⚠️ Hardware Compatibility<br/>Direct Interface Support]
    end
    
    %% Styling
    classDef ipcSystem fill:#e8f5e8
    classDef directPath fill:#e3f2fd
    classDef bypassed fill:#ffebee
    classDef hardware fill:#fce4ec
    classDef software fill:#fff3e0
    classDef advantages fill:#e8f5e8
    classDef challenges fill:#fff3e0
    classDef options fill:#f3e5f5
    
    class IPC,USB3,ADAPTER ipcSystem
    class DIRECT_NET,NANO_X_DIRECT,NANO_Z_DIRECT,RECOATER_DIRECT directPath
    class BYPASS_CTRL,BYPASS_NET bypassed
    class MOTOR_X_PROP,MOTOR_Z_PROP,GRIPPER_PROP,DRUM1_PROP,DRUM2_PROP,DRUM3_PROP,PRESSURE_PROP hardware
    class API_CLIENT,MOTION_LIB,RECOATER_LIB,SAFETY_LIB software
    class ADV1,ADV2,ADV3,ADV4 advantages
    class CHAL1,CHAL2,CHAL3,CHAL4 challenges
    class OPT1,OPT2,OPT3 options
```

## Implementation Strategy

### Hardware Connection
1. **USB3-to-RJ45 Adapter**: Connect IPC directly to recoater network
2. **Direct IP Assignment**: Configure motor drivers with new IP addresses
3. **Point-to-Point Network**: Eliminate dependency on existing network infrastructure

### Software Architecture
1. **Custom API Client**: Build your own control interface
2. **Motion Control Library**: Direct Modbus TCP communication with Nanotec drivers
3. **Recoater Control Library**: Interface directly with 3-drum system
4. **Safety Management**: Software-based safety interlocks and monitoring

### Implementation Options
1. **Option 1**: Reverse engineer existing API protocol for full compatibility
2. **Option 2**: Direct Modbus control for motor-only applications
3. **Option 3**: Hybrid approach combining API and direct control

### Advantages
- ✓ **Direct Control**: No dependency on Controllino PLC
- ✓ **Custom Software**: Full flexibility in implementation
- ✓ **Simplified Network**: Point-to-point connection
- ✓ **Cost Effective**: Simple USB3-to-RJ45 adapter solution

### Challenges
- ⚠️ **Safety Systems**: Must implement software-based safety
- ⚠️ **Protocol Reverse Engineering**: Need to understand recoater communication
- ⚠️ **Real-time Control**: Timing requirements for precise control
- ⚠️ **Hardware Compatibility**: Ensure direct interface support
