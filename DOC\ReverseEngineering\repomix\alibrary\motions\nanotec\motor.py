"""Defines a class that controls a motor connected to a NanotecDriver.

This class implements the NanotecDriver class. It is a subclass of both
NanotecDriver and Motor. It has to define the Motor methods.
"""

from dataclasses import dataclass

from alibrary.electronics.nanotec import NanotecDriver, NanotecDriverError
from alibrary.motions.abstract.command import MotionType
from alibrary.motions.abstract.motor import Motor
from alibrary.motions.nanotec.command import NanotecMotionCommand
from alibrary.server import BadRequestError, ConflictError, InternalServerError


@dataclass
class NanotecMotorConfig:
    """Configuration variables of a Nanotec BLDC motor.

    Attributes:
        max_speed: The maximum speed allowed by the motor [mm/z]
        min_abs_distance: The minimum absolute position of the motor [mm]
        max_abs_distance: The maximum absolute position of the motor [mm]
        homing_speed: The speed of the homing procedure [mm/s]
        homing_acceleration: The acceleration of the homing procedure [mm/s²]
        search_zero_speed: The speed of the search zero procedure [mm/s]
        acceleration: The acceleration of the motions [mm/s²]
        deceleration: The decelerations of the motions [mm/s²]
        stop_deceleration: The deceleration when stopping the motions [mm/s²]
    """
    max_speed: float = 0.0
    min_abs_distance: float = 0.0
    max_abs_distance: float = 0.0
    homing_speed: float = 0.0
    homing_acceleration: float = 0.0
    search_zero_speed: float = 0.0
    acceleration: float = 0.0
    deceleration: float = 0.0
    stop_deceleration: float = 0.0


class NanotecMotor(Motor):
    """Implementation of the Motor class for a motor connected to a Nanotec
    driver.
    """

    def __init__(
        self,
        driver: NanotecDriver,
        config: NanotecMotorConfig,
        should_be_homed: bool = False,
        should_use_custom_homing: bool = False,
        should_halt: bool = False,
    ) -> None:
        self.config = config
        self.driver = driver
        self.__should_be_homed = should_be_homed
        self.__should_use_custom_homing = should_use_custom_homing
        self.__should_halt = should_halt

    def is_busy(self) -> bool:
        """Returns the running status of the motor.

        Returns:
            True if a motion is running on the motor, false otherwise

        Raises:
            InternalServerError: An error occurs in the process
        """
        try:
            return self.driver.is_busy()
        except NanotecDriverError as error:
            raise InternalServerError(str(error)) from error

    def get_position(self) -> float:
        """Gets the current position of the Nanotec driver.

        Returns:
            A float representing the position in mm

        Raises:
            InternalServerError: An error occurs in the process
        """
        try:
            position = self.driver.get_position()
            return position / 1000
        except NanotecDriverError as error:
            raise InternalServerError(str(error)) from error

    def is_homed(self) -> bool:
        """Returns the homing status of the motor.

        Returns:
            True if a homing has been performed on the motor, false otherwise

        Raises:
            InternalServerError: An error occurs in the process
        """
        try:
            return self.driver.is_homed()
        except NanotecDriverError as error:
            raise InternalServerError(str(error)) from error

    def validate_command(self, command: NanotecMotionCommand,
                         min_abs_distance: float, max_abs_distance: float):
        """Checks if the command is valid regarding to the motor current state
        and parameters.

        In addition of its parent class validation, it also checks if the given
        speed is valid

        Raises:
            BadRequestError: The given command is not valid
            InternalServerError: An error occurs in the process
        """
        if command.motion_type == MotionType.HOMING:
            return

        # Speed must be in ]0; max_speed]
        if command.speed <= 0 or command.speed > self.config.max_speed:
            raise BadRequestError("Wrong speed value, must be below "
                                  f"{self.config.max_speed} mm/s")

        super().validate_command(command, min_abs_distance, max_abs_distance)

    def start(self, command: NanotecMotionCommand):
        """Starts a motion following the given motion command.

        It will first call the parent method to check if there is no motion
        currently running. Then it checks if the command is valid before
        starting the motion.

        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        super().start(command)

        # Validate motion command
        self.validate_command(command, self.config.min_abs_distance,
                              self.config.max_abs_distance)

        if self.driver.offline:
            return

        # Check homing
        if (self.__should_be_homed and not self.driver.is_homed() and
                command.motion_type != MotionType.HOMING):
            raise ConflictError("Homing not done")

        # Select right method
        if command.motion_type == MotionType.HOMING:
            if self.__should_use_custom_homing:
                self.driver.perform_custom_homing(
                    int(self.config.homing_speed * 1000),
                    int(self.config.homing_acceleration * 1000),
                )
            else:
                self.driver.perform_homing(
                    int(self.config.homing_speed * 1000),
                    int(self.config.homing_acceleration * 1000),
                    int(self.config.search_zero_speed * 1000),
                )
        elif command.motion_type == MotionType.SPEED:
            self.driver.perform_speed_motion(
                int(command.speed * 1000),
                int(self.config.acceleration * 1000),
            )
        else:
            is_relative = command.motion_type == MotionType.RELATIVE
            self.driver.perform_distance_motion(
                int(command.distance * 1000),
                int(command.speed * 1000),
                int(self.config.acceleration * 1000),
                int(self.config.deceleration * 1000),
                is_relative,
            )

        self.current_command = command

    def stop(self):
        if self.driver.offline:
            return

        if self.__should_halt:
            self.driver.halt()
        else:
            self.driver.stop(int(self.config.stop_deceleration * 1000))
