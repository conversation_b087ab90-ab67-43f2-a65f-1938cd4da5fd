#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to recursively remove all __pycache__ directories and .pyc files
from a given directory.
"""

import os
import shutil
import argparse

def remove_pycache(root_dir):
    """
    Recursively remove all __pycache__ directories and .pyc files from root_dir.
    
    Args:
        root_dir (str): The root directory to start the search from
    
    Returns:
        tuple: (count of removed directories, count of removed files)
    """
    pycache_dirs_removed = 0
    pyc_files_removed = 0
    
    print(f"Searching for __pycache__ directories and .pyc files in: {root_dir}")
    
    # First collect all __pycache__ directories and .pyc files
    pycache_dirs = []
    pyc_files = []
    
    for dirpath, dirnames, filenames in os.walk(root_dir, topdown=False):
        # Collect __pycache__ directories
        for dirname in dirnames:
            if dirname == "__pycache__":
                pycache_path = os.path.join(dirpath, dirname)
                pycache_dirs.append(pycache_path)
        
        # Collect .pyc files
        for filename in filenames:
            if filename.endswith(".pyc") or filename.endswith(".pyo"):
                file_path = os.path.join(dirpath, filename)
                pyc_files.append(file_path)
    
    # Remove collected __pycache__ directories
    for pycache_path in pycache_dirs:
        if os.path.exists(pycache_path):
            try:
                shutil.rmtree(pycache_path)
                print(f"Removed directory: {pycache_path}")
                pycache_dirs_removed += 1
            except Exception as e:
                print(f"Error removing {pycache_path}: {e}")
    
    # Remove collected .pyc files
    for file_path in pyc_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"Removed file: {file_path}")
                pyc_files_removed += 1
            except Exception as e:
                print(f"Error removing {file_path}: {e}")
    
    return pycache_dirs_removed, pyc_files_removed

def main():
    parser = argparse.ArgumentParser(description="Remove Python cache files and directories")
    parser.add_argument(
        "--dir", 
        default=os.getcwd(),
        help="Directory to clean (default: current directory)"
    )
    parser.add_argument(
        "--quiet", 
        action="store_true",
        help="Suppress detailed output"
    )
    
    args = parser.parse_args()
    
    # Store original stdout for later restoration
    if args.quiet:
        original_stdout = os.sys.stdout
        os.sys.stdout = open(os.devnull, 'w')
    
    try:
        dirs_removed, files_removed = remove_pycache(args.dir)
        
        # Restore stdout if it was redirected
        if args.quiet:
            os.sys.stdout = original_stdout
        
        print(f"\nCleanup complete!")
        print(f"Removed {dirs_removed} __pycache__ directories")
        print(f"Removed {files_removed} .pyc/.pyo files")
    except Exception as e:
        # Restore stdout if it was redirected
        if args.quiet:
            os.sys.stdout = original_stdout
        print(f"Error during cleanup: {e}")

if __name__ == "__main__":
    main()