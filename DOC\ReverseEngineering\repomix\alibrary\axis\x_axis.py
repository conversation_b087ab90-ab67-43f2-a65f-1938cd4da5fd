"""Module defining an X axis, a subclass of the abstract Axis class."""
from alibrary.axis.axis import Axis

from alibrary.motions.abstract.motor import Motor


class XAxis(Axis):
    def __init__(
        self,
        motor: Motor,
        patterning_speed: float,
        travel_speed: float,
    ) -> None:
        super().__init__(motor)
        self.patterning_speed = patterning_speed
        self.travel_speed = travel_speed
