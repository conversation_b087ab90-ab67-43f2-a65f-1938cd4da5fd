"""Mo<PERSON>les defining all the controllers of the server."""
from threading import Thread
import requests

from alibrary.motions.nanotec import Nanotec<PERSON><PERSON><PERSON>ommand
from alibrary.server import ConflictError
from alibrary.motions import MotionType
from connexion import NoContent
from flask import make_response

from . import models as m
from . import config as c


# X AXIS =======================================================================
def get_x_info():
    return m.x_axis.get_info()


def get_x_motion():
    return m.x_axis.get_command()


def set_x_motion(body: dict[str,]):
    if m.WAS_EMS_ACTIVATED.is_set() or m.WAS_COVER_OPEN.is_set():
        raise ConflictError("EMS activated or cover interlock opened.")
    m.x_axis.start_motion(NanotecMotionCommand.from_json(body))
    return NoContent, 201


def cancel_x_motion():
    m.x_axis.stop_motion()


# Z AXIS =======================================================================
def get_z_info():
    return m.z_axis.get_info()


def get_z_motion():
    if m.WAS_DOOR_OPEN.is_set() or m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("Door interlock opened.")
    return m.z_axis.get_command()


def set_z_motion(body: dict[str,]):
    if m.WAS_DOOR_OPEN.is_set() or m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("Door interlock opened.")
    motion = NanotecMotionCommand.from_json(body)
    if motion.motion_type == MotionType.HOMING:
        m.z_axis.gripper.set_gripper_state({"state": False})
    m.z_axis.start_motion(motion)
    Thread(target=m.axis_position.moving).start()
    return NoContent, 201


def cancel_z_motion():
    if m.WAS_DOOR_OPEN.is_set() or m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("Door interlock opened.")
    m.z_axis.stop_motion()


# Z GRIPPER ====================================================================


def get_gripper_state():
    return m.z_axis.gripper.get_gripper_state()


def put_gripper_state(body: dict[str,]):
    m.z_axis.gripper.set_gripper_state(body)


# GEOMETRIES ===================================================================
def put_drum_geometry(drum_id, body):
    if body[1:4] == b"PNG":
        m.printer.set_drum_png(drum_id, body)
    else:
        m.printer.set_drum_cli(drum_id, body)


def delete_drum_geometry(drum_id):
    m.printer.remove_geometry(drum_id)


# PREVIEW ======================================================================
def get_preview(layer_id: int):
    if layer_id > m.printer.get_info()["n_layers"]:
        return NoContent, 404

    response = make_response(m.t.get_preview(layer_id))
    response.mimetype = "image/png"
    return response


# STATE ======================================================================
def get_state():
    """Returns the state of the printer"""
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"
    response = requests.get(f"{base_url}/state", timeout=3.0)
    json: dict = response.json()
    state = json["state"]
    if m.executor.has_errors() or state == "error":
        state = "error"
    elif m.WAS_COVER_OPEN.is_set() or m.WAS_DOOR_OPEN.is_set():
        state = "waiting_for_reset"
    elif m.executor.is_running() or state == "printing":
        state = "printing"
    else:
        state = "ready"

    return {
        "state": state,
    }


# PRINT ========================================================================
def get_print_info():
    json = m.printer.get_info()
    with m.last_printed_layer_index.get_lock():
        json["last_layer"] = m.last_printed_layer_index.value
    return json


def are_drums_running() -> bool:
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"
    response = requests.get(f"{base_url}/drums", timeout=3.0)
    drums: dict = response.json()

    running = False
    for drum in drums:
        running |= drum["running"]

    return running


def start_print_job():
    if m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("EMS activated.")
    if not m.x_axis.motor.is_homed():
        raise ConflictError("X axis is not homed.")

    if are_drums_running():
        raise ConflictError("Drums moving. Stop them before printing.")
    m.executor.start()


def cancel_print_job():
    m.executor.stop()


def get_print_parameters():
    """Returns the print parameters"""
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"

    response = requests.get(f"{base_url}/layer/parameters", timeout=3.0)

    layer_param: dict = response.json()

    x_axis_param = {
        "patterning_speed": m.x_axis.patterning_speed,
        "travel_speed": m.x_axis.travel_speed,
    }

    z_axis_param = {
        "z_speed": m.z_axis.speed,
        "z_offset": m.z_axis.offset,
        "layer_thickness": m.z_axis.layer_thickness,
    }

    param = layer_param | x_axis_param | z_axis_param

    param.pop("speed")

    param["layer_start"] = m.printer.start_layer_index
    param["layer_end"] = m.printer.end_layer_index
    param["collectors_delay"] = m.printer.collectors_delay

    return param


def put_print_parameters(body: dict[str,]):
    """Sets the printing parameters"""
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"

    if "patterning_speed" in body:
        m.x_axis.patterning_speed = float(body["patterning_speed"])

    if "travel_speed" in body:
        m.x_axis.travel_speed = float(body["travel_speed"])

    if "layer_start" in body:
        m.printer.start_layer_index = int(body["layer_start"])

    if "layer_end" in body:
        m.printer.end_layer_index = int(body["layer_end"])

    if "z_speed" in body:
        m.z_axis.speed = float(body["z_speed"])

    if "z_offset" in body:
        m.z_axis.offset = float(body["z_offset"])

    if "layer_thickness" in body:
        m.z_axis.layer_thickness = float(body["layer_thickness"])

    if "collectors_delay" in body:
        m.printer.collectors_delay = int(body["collectors_delay"])

    body["speed"] = body["patterning_speed"]
    requests.put(f"{base_url}/layer/parameters", json=body, timeout=3.0)


def set_light(body: dict[str,]):
    m.controllino.set_chamber_light_state(body["state"])


def get_light() -> dict[str,]:
    state = m.controllino.get_chamber_light_state()
    return {
        "state": state,
    }
