"""Module describing a pressure regulator for the ejection pressure of the
recoater.
"""
from alibrary.electronics.controllino.plc import ControllinoError
from alibrary.pneumatic.pressure_regulator import PressureRegulator
from alibrary.electronics.controllino import Controllino
from alibrary.server import InternalServerError
from alibrary.logger import logger


class TubesEjectionPressureRegulator(PressureRegulator):
    """Implementation of PressureRegulator for the ejection pressure inside a
    a recoater with tubes.

    Attributes:
        controllino: A Controllino object
        drum_id: An integer representing the drum of which to modify the
        ejection
    """

    def __init__(self, controllino: Controllino, drum_id: int,
                 maximum: float) -> None:
        super().__init__(maximum * 100000)
        self.controllino: Controllino = controllino
        self.drum_id: int = drum_id

    @property
    def pressure(self) -> float:
        """Getter for the current pressure"""
        max_in_bar = self.maximum / 100000
        raw_pressure = self.controllino.get_ejection(self.drum_id)
        pressure = (raw_pressure - 51) / 204 * max_in_bar
        return pressure * 100000

    @PressureRegulator.target.setter
    def target(self, pressure: float) -> None:
        """Setter for the target pressure"""
        try:
            max_in_bar = self.maximum / 100000
            pressure_in_bar = pressure / 100000
            raw_pressure = pressure_in_bar * 204 / max_in_bar + 51
            self.controllino.set_ejection(self.drum_id, raw_pressure)
            self._target = pressure
        except ControllinoError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
