"""Module defining all the models that are used in teh server."""
from threading import Thread
import time
from multiprocessing import Value, Event
import requests

from alibrary.electronics import Controllino, ControllinoPLC, NanotecDriver
from alibrary.motions.nanotec import <PERSON>otecMotor, NanotecMotorConfig
from alibrary.axis import X<PERSON>xis, <PERSON>Axis
from alibrary.axis.gripper import Gripper
from alibrary.recoater.executor import ProcedureExecutor
from alibrary.printer import Printer
from alibrary import logger

from . import config as c
from . import z_axis_position as a
from . import tasks as t

# Controllino
controllino = Controllino(
    n_drums=c.N_DRUMS,
    plcs=[
        ControllinoPLC(
            ip=c.IP_CONTROLLINO,
            port=c.PORT_CONTROLLINO,
            offline=c.OFFLINE_MODE,
        ),
    ],
)

# ==============================================================================

logger.info("Checking EMS deactivated.")
while (not controllino.is_ems_deactivated() or
       not controllino.is_door_interlock_closed()):
    time.sleep(0.1)
logger.info("EMS deactivated.")
time.sleep(10)

x_config = NanotecMotorConfig(
    max_speed=c.X_MAX_SPEED,
    min_abs_distance=c.X_MIN_POSITION,
    max_abs_distance=c.X_MAX_POSITION,
    homing_speed=c.X_HOMING_SPEED,
    homing_acceleration=c.X_HOMING_ACCELERATION,
    search_zero_speed=c.X_SEARCH_ZERO_SPEED,
    acceleration=c.X_ACCELERATION,
    deceleration=c.X_DECELERATION,
    stop_deceleration=c.X_STOP_DECELERATION,
)

x_driver = NanotecDriver(
    ip=c.IP_NANOTEC_X,
    offline=c.OFFLINE_MODE,
)

x_axis = XAxis(
    motor=NanotecMotor(
        driver=x_driver,
        config=x_config,
        should_be_homed=True,
        should_halt=True,
    ),
    patterning_speed=0.0,
    travel_speed=0.0,
)

z_gripper = Gripper(controllino=controllino)

z_config = NanotecMotorConfig(
    max_speed=c.Z_MAX_SPEED,
    min_abs_distance=c.Z_MIN_POSITION,
    max_abs_distance=c.Z_MAX_POSITION,
    homing_speed=c.Z_HOMING_SPEED,
    homing_acceleration=c.Z_HOMING_ACCELERATION,
    search_zero_speed=c.Z_SEARCH_ZERO_SPEED,
    acceleration=c.Z_ACCELERATION,
    deceleration=c.Z_DECELERATION,
    stop_deceleration=c.Z_STOP_DECELERATION,
)

z_driver = NanotecDriver(
    ip=c.IP_NANOTEC_Z,
    offline=c.OFFLINE_MODE,
)

z_axis = ZAxis(
    motor=NanotecMotor(
        driver=z_driver,
        config=z_config,
        should_be_homed=True,
        should_halt=True,
    ),
    speed=0.0,
    offset=0.0,
    layer_thickness=0.0,
    gripper=z_gripper,
)

axis_position = a.ZAxisPosition()

EMS_HAS_BEEN_ACTIVATED = False
WAS_DOOR_OPEN = Event()
WAS_COVER_OPEN = Event()
WAS_EMS_ACTIVATED = Event()
Z_AXIS_POSITION = Value("f", -1)
SHOULD_LOOP = True

# ==============================================================================

printer = Printer()

# Set default parameters
x_axis.patterning_speed = c.PATTERNING_SPEED
x_axis.travel_speed = c.TRAVEL_SPEED

z_axis.speed = c.Z_SPEED
z_axis.offset = c.Z_OFFSET
z_axis.layer_thickness = c.LAYER_THICKNESS

printer.start_layer_index = c.LAYER_START
printer.end_layer_index = c.LAYER_END
printer.collectors_delay = c.COLLECTORS_DELAY

# Set recoater default parameters
json = {"speed": c.PATTERNING_SPEED, "x_offset": c.X_OFFSET, "filling_id": -1}
requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/layer/parameters",
             json=json,
             timeout=3.0)

# Set recoater default config
response = requests.get(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
                        timeout=3.0)
json = response.json()
json["build_space_diameter"] = c.DEFAULT_DIE_DIAMETER

if "build_space_dimensions" in json:
    json.pop("build_space_dimensions")

requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
             json=json,
             timeout=3.0)

executor = ProcedureExecutor(
    name="Printing",
    procedure=t.print_procedure,
    cancel_procedure=t.cancel_procedure,
)

last_printed_layer_index = Value("i", 0)
Thread(name="monitor_door", target=t.monitor_door, daemon=True).start()
Thread(name="monitor_cover", target=t.monitor_cover, daemon=True).start()
Thread(name="monitor_ems", target=t.monitor_ems, daemon=True).start()
