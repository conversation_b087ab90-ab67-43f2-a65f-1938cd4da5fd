# Directory Structure
```
__init__.py/
  __init__.py
__main__.py/
  __main__.py
app.py/
  app.py
config.py/
  config.py
controllers.py/
  controllers.py
models.py/
  models.py
openapi.yaml/
  openapi.yaml
tasks.py/
  tasks.py
z_axis_position.py/
  z_axis_position.py
```

# Files

## File: __init__.py/__init__.py
```python
"""Entry point of the Connexion server.
This file defines the factory method that build the Flask app.
"""
import os
from importlib.metadata import PackageNotFoundError, version
import yaml
try:
    __version__ = version(__name__)
except PackageNotFoundError:
    __version__ = "0.0.1"
api_file = os.path.dirname(__file__) + "/openapi.yaml"
with open(api_file, "r", encoding="utf-8") as fh:
    API_VERSION = yaml.safe_load(fh)["info"]["version"]
LOGO = r"""
    ___                        _       __
   /   | ___  _________  _____(_)___  / /_
  / /| |/ _ \/ ___/ __ \/ ___/ / __ \/ __/
 / ___ /  __/ /  / /_/ (__  ) / / / / /_
/_/  |_\___/_/   \____/____/_/_/ /_/\__/"""
COLORED_LOGO = "\x1b[36;20m" + LOGO + "\x1b[0m"
```

## File: __main__.py/__main__.py
```python
"""Entry-point of the production server"""
import click
from gunicorn.app.base import BaseApplication
from alibrary.logger import config_logger
from aprinter.app import create_app
from aprinter import __version__
class StandaloneApplication(BaseApplication):
    """Gunicorn application that will run the server"""
    def __init__(self,
                 bind="127.0.0.1:8080",
                 timeout=180,
                 reload_server=False):
        self.app = create_app()
        self.bind = bind
        self.timeout = timeout
        self.reload_server = reload_server
        super().__init__(None, None)
    def init(self, parser, opts, args):
        pass
    def load_config(self):
        s = self.cfg.set
        s("bind", self.bind)
        s("timeout", self.timeout)
        s("reload", self.reload_server)
    def load(self):
        return self.app
CONTEXT_SETTINGS = dict(help_option_names=["-h", "--help"])
@click.command(context_settings=CONTEXT_SETTINGS)
@click.option(
    "-b",
    "--bind",
    metavar="ADDRESS",
    type=str,
    default="127.0.0.1:8080",
    show_default=True,
    help="The socket to bind.",
)
@click.option(
    "-t",
    "--timeout",
    type=int,
    default=180,
    show_default=True,
    help=
    "Workers silent for more than this many seconds are killed and restarted.",
)
@click.option(
    "-r",
    "--reload",
    "reload_server",
    is_flag=True,
    default=False,
    show_default=True,
    help="Restart workers when code changes.",
)
@click.option(
    "-d",
    "--debug",
    is_flag=True,
    default=False,
    show_default=True,
    help="Increase logs verbosity.",
)
@click.version_option(__version__, "-v", "--version")
def main(bind, timeout, reload_server, debug):
    """Starts the production server"""
    if debug:
        config_logger(debug=True)
    app = StandaloneApplication(bind=bind,
                                timeout=timeout,
                                reload_server=reload_server)
    app.run()
if __name__ == "__main__":
    StandaloneApplication().run()
```

## File: app.py/app.py
```python
""""Module defining the app factory function.
This factory function is used by Flask and Connexion to generate the server.
"""
from alibrary import init_logger, logger
from alibrary.server import CustomHttpError, custom_error_handler, enable_cors
from werkzeug.exceptions import HTTPException
from flask import Flask
from connexion import FlaskApp
from connexion.exceptions import ProblemException
from connexion.resolver import RelativeResolver
from . import COLORED_LOGO, __version__, api_file
from . import config
def create_app() -> Flask:
    """Creates a Connexion application.
    Returns:
        A Flask object
    """
    # Create a FlaskApp object
    connexion_app = FlaskApp(__name__)
    # Add API
    connexion_app.add_api(api_file,
                          resolver=RelativeResolver("aprinter.controllers"),
                          arguments={"title": "Aerosint API"},
                          strict_validation=True,
                          pythonic_params=True,)
    # Change error handlers
    connexion_app.add_error_handler(CustomHttpError, custom_error_handler)
    connexion_app.add_error_handler(ProblemException, custom_error_handler)
    connexion_app.add_error_handler(HTTPException, custom_error_handler)
    # Initialize logger
    init_logger(connexion_app.app)
    enable_cors(connexion_app.app)
    # Print logo
    print(f"{COLORED_LOGO} {config.RECOATER_NAME} v{__version__}\n\n")
    logger.info("Recoater started")
    if config.OFFLINE_MODE:
        logger.warning("Offline mode enabled")
    return connexion_app.app
```

## File: config.py/config.py
```python
"""Configuration files with project specific variables."""
# Recoater name
RECOATER_NAME = "Aprinter"
ASERVER_ADDRESS = "127.0.0.1:8080"
# IP addresses
IP_CONTROLLINO = "***********"
IP_NANOTEC_X = "************"
IP_NANOTEC_Z = "************"
PORT_CONTROLLINO = 4080
# The number of Drums on the recoater
N_DRUMS = 3
# The mode of the server
OFFLINE_MODE = False
# X Axis
X_MAX_SPEED = 250
X_MIN_POSITION = 0
X_MAX_POSITION = 800
X_LEFT_PRINT_POSITION = 150
X_RIGHT_PRINT_POSITION = 790
X_HOMING_SPEED = 15
X_HOMING_ACCELERATION = 1000
X_SEARCH_ZERO_SPEED = 1
X_ACCELERATION = 250
X_DECELERATION = 250
X_STOP_DECELERATION = 1000
# Z Axis
Z_MAX_SPEED = 10
Z_MIN_POSITION = 0
Z_MAX_POSITION = 120
Z_HOMING_SPEED = 5
Z_HOMING_ACCELERATION = 10
Z_SEARCH_ZERO_SPEED = 1
Z_ACCELERATION = 10
Z_DECELERATION = 10
Z_STOP_DECELERATION = 15
# Default parameters
COLLECTORS_DELAY = 10
LAYER_START = 1
LAYER_END = 1
LAYER_THICKNESS = 0.08
PATTERNING_SPEED = 30
TRAVEL_SPEED = 100
Z_OFFSET = 2
Z_SPEED = 5
X_OFFSET = 41.5
DEFAULT_DIE_DIAMETER = 40
```

## File: controllers.py/controllers.py
```python
"""Modules defining all the controllers of the server."""
from threading import Thread
import requests
from alibrary.motions.nanotec import NanotecMotionCommand
from alibrary.server import ConflictError
from alibrary.motions import MotionType
from connexion import NoContent
from flask import make_response
from . import models as m
from . import config as c
# X AXIS =======================================================================
def get_x_info():
    return m.x_axis.get_info()
def get_x_motion():
    return m.x_axis.get_command()
def set_x_motion(body: dict[str,]):
    if m.WAS_EMS_ACTIVATED.is_set() or m.WAS_COVER_OPEN.is_set():
        raise ConflictError("EMS activated or cover interlock opened.")
    m.x_axis.start_motion(NanotecMotionCommand.from_json(body))
    return NoContent, 201
def cancel_x_motion():
    m.x_axis.stop_motion()
# Z AXIS =======================================================================
def get_z_info():
    return m.z_axis.get_info()
def get_z_motion():
    if m.WAS_DOOR_OPEN.is_set() or m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("Door interlock opened.")
    return m.z_axis.get_command()
def set_z_motion(body: dict[str,]):
    if m.WAS_DOOR_OPEN.is_set() or m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("Door interlock opened.")
    motion = NanotecMotionCommand.from_json(body)
    if motion.motion_type == MotionType.HOMING:
        m.z_axis.gripper.set_gripper_state({"state": False})
    m.z_axis.start_motion(motion)
    Thread(target=m.axis_position.moving).start()
    return NoContent, 201
def cancel_z_motion():
    if m.WAS_DOOR_OPEN.is_set() or m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("Door interlock opened.")
    m.z_axis.stop_motion()
# Z GRIPPER ====================================================================
def get_gripper_state():
    return m.z_axis.gripper.get_gripper_state()
def put_gripper_state(body: dict[str,]):
    m.z_axis.gripper.set_gripper_state(body)
# GEOMETRIES ===================================================================
def put_drum_geometry(drum_id, body):
    if body[1:4] == b"PNG":
        m.printer.set_drum_png(drum_id, body)
    else:
        m.printer.set_drum_cli(drum_id, body)
def delete_drum_geometry(drum_id):
    m.printer.remove_geometry(drum_id)
# PREVIEW ======================================================================
def get_preview(layer_id: int):
    if layer_id > m.printer.get_info()["n_layers"]:
        return NoContent, 404
    response = make_response(m.t.get_preview(layer_id))
    response.mimetype = "image/png"
    return response
# STATE ======================================================================
def get_state():
    """Returns the state of the printer"""
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"
    response = requests.get(f"{base_url}/state", timeout=3.0)
    json: dict = response.json()
    state = json["state"]
    if m.executor.has_errors() or state == "error":
        state = "error"
    elif m.WAS_COVER_OPEN.is_set() or m.WAS_DOOR_OPEN.is_set():
        state = "waiting_for_reset"
    elif m.executor.is_running() or state == "printing":
        state = "printing"
    else:
        state = "ready"
    return {
        "state": state,
    }
# PRINT ========================================================================
def get_print_info():
    json = m.printer.get_info()
    with m.last_printed_layer_index.get_lock():
        json["last_layer"] = m.last_printed_layer_index.value
    return json
def are_drums_running() -> bool:
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"
    response = requests.get(f"{base_url}/drums", timeout=3.0)
    drums: dict = response.json()
    running = False
    for drum in drums:
        running |= drum["running"]
    return running
def start_print_job():
    if m.WAS_EMS_ACTIVATED.is_set():
        raise ConflictError("EMS activated.")
    if not m.x_axis.motor.is_homed():
        raise ConflictError("X axis is not homed.")
    if are_drums_running():
        raise ConflictError("Drums moving. Stop them before printing.")
    m.executor.start()
def cancel_print_job():
    m.executor.stop()
def get_print_parameters():
    """Returns the print parameters"""
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"
    response = requests.get(f"{base_url}/layer/parameters", timeout=3.0)
    layer_param: dict = response.json()
    x_axis_param = {
        "patterning_speed": m.x_axis.patterning_speed,
        "travel_speed": m.x_axis.travel_speed,
    }
    z_axis_param = {
        "z_speed": m.z_axis.speed,
        "z_offset": m.z_axis.offset,
        "layer_thickness": m.z_axis.layer_thickness,
    }
    param = layer_param | x_axis_param | z_axis_param
    param.pop("speed")
    param["layer_start"] = m.printer.start_layer_index
    param["layer_end"] = m.printer.end_layer_index
    param["collectors_delay"] = m.printer.collectors_delay
    return param
def put_print_parameters(body: dict[str,]):
    """Sets the printing parameters"""
    base_url = f"http://{c.ASERVER_ADDRESS}/api/v3"
    if "patterning_speed" in body:
        m.x_axis.patterning_speed = float(body["patterning_speed"])
    if "travel_speed" in body:
        m.x_axis.travel_speed = float(body["travel_speed"])
    if "layer_start" in body:
        m.printer.start_layer_index = int(body["layer_start"])
    if "layer_end" in body:
        m.printer.end_layer_index = int(body["layer_end"])
    if "z_speed" in body:
        m.z_axis.speed = float(body["z_speed"])
    if "z_offset" in body:
        m.z_axis.offset = float(body["z_offset"])
    if "layer_thickness" in body:
        m.z_axis.layer_thickness = float(body["layer_thickness"])
    if "collectors_delay" in body:
        m.printer.collectors_delay = int(body["collectors_delay"])
    body["speed"] = body["patterning_speed"]
    requests.put(f"{base_url}/layer/parameters", json=body, timeout=3.0)
def set_light(body: dict[str,]):
    m.controllino.set_chamber_light_state(body["state"])
def get_light() -> dict[str,]:
    state = m.controllino.get_chamber_light_state()
    return {
        "state": state,
    }
```

## File: models.py/models.py
```python
"""Module defining all the models that are used in teh server."""
from threading import Thread
import time
from multiprocessing import Value, Event
import requests
from alibrary.electronics import Controllino, ControllinoPLC, NanotecDriver
from alibrary.motions.nanotec import NanotecMotor, NanotecMotorConfig
from alibrary.axis import XAxis, ZAxis
from alibrary.axis.gripper import Gripper
from alibrary.recoater.executor import ProcedureExecutor
from alibrary.printer import Printer
from alibrary import logger
from . import config as c
from . import z_axis_position as a
from . import tasks as t
# Controllino
controllino = Controllino(
    n_drums=c.N_DRUMS,
    plcs=[
        ControllinoPLC(
            ip=c.IP_CONTROLLINO,
            port=c.PORT_CONTROLLINO,
            offline=c.OFFLINE_MODE,
        ),
    ],
)
# ==============================================================================
logger.info("Checking EMS deactivated.")
while (not controllino.is_ems_deactivated() or
       not controllino.is_door_interlock_closed()):
    time.sleep(0.1)
logger.info("EMS deactivated.")
time.sleep(10)
x_config = NanotecMotorConfig(
    max_speed=c.X_MAX_SPEED,
    min_abs_distance=c.X_MIN_POSITION,
    max_abs_distance=c.X_MAX_POSITION,
    homing_speed=c.X_HOMING_SPEED,
    homing_acceleration=c.X_HOMING_ACCELERATION,
    search_zero_speed=c.X_SEARCH_ZERO_SPEED,
    acceleration=c.X_ACCELERATION,
    deceleration=c.X_DECELERATION,
    stop_deceleration=c.X_STOP_DECELERATION,
)
x_driver = NanotecDriver(
    ip=c.IP_NANOTEC_X,
    offline=c.OFFLINE_MODE,
)
x_axis = XAxis(
    motor=NanotecMotor(
        driver=x_driver,
        config=x_config,
        should_be_homed=True,
        should_halt=True,
    ),
    patterning_speed=0.0,
    travel_speed=0.0,
)
z_gripper = Gripper(controllino=controllino)
z_config = NanotecMotorConfig(
    max_speed=c.Z_MAX_SPEED,
    min_abs_distance=c.Z_MIN_POSITION,
    max_abs_distance=c.Z_MAX_POSITION,
    homing_speed=c.Z_HOMING_SPEED,
    homing_acceleration=c.Z_HOMING_ACCELERATION,
    search_zero_speed=c.Z_SEARCH_ZERO_SPEED,
    acceleration=c.Z_ACCELERATION,
    deceleration=c.Z_DECELERATION,
    stop_deceleration=c.Z_STOP_DECELERATION,
)
z_driver = NanotecDriver(
    ip=c.IP_NANOTEC_Z,
    offline=c.OFFLINE_MODE,
)
z_axis = ZAxis(
    motor=NanotecMotor(
        driver=z_driver,
        config=z_config,
        should_be_homed=True,
        should_halt=True,
    ),
    speed=0.0,
    offset=0.0,
    layer_thickness=0.0,
    gripper=z_gripper,
)
axis_position = a.ZAxisPosition()
EMS_HAS_BEEN_ACTIVATED = False
WAS_DOOR_OPEN = Event()
WAS_COVER_OPEN = Event()
WAS_EMS_ACTIVATED = Event()
Z_AXIS_POSITION = Value("f", -1)
SHOULD_LOOP = True
# ==============================================================================
printer = Printer()
# Set default parameters
x_axis.patterning_speed = c.PATTERNING_SPEED
x_axis.travel_speed = c.TRAVEL_SPEED
z_axis.speed = c.Z_SPEED
z_axis.offset = c.Z_OFFSET
z_axis.layer_thickness = c.LAYER_THICKNESS
printer.start_layer_index = c.LAYER_START
printer.end_layer_index = c.LAYER_END
printer.collectors_delay = c.COLLECTORS_DELAY
# Set recoater default parameters
json = {"speed": c.PATTERNING_SPEED, "x_offset": c.X_OFFSET, "filling_id": -1}
requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/layer/parameters",
             json=json,
             timeout=3.0)
# Set recoater default config
response = requests.get(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
                        timeout=3.0)
json = response.json()
json["build_space_diameter"] = c.DEFAULT_DIE_DIAMETER
if "build_space_dimensions" in json:
    json.pop("build_space_dimensions")
requests.put(f"http://{c.ASERVER_ADDRESS}/api/v3/config",
             json=json,
             timeout=3.0)
executor = ProcedureExecutor(
    name="Printing",
    procedure=t.print_procedure,
    cancel_procedure=t.cancel_procedure,
)
last_printed_layer_index = Value("i", 0)
Thread(name="monitor_door", target=t.monitor_door, daemon=True).start()
Thread(name="monitor_cover", target=t.monitor_cover, daemon=True).start()
Thread(name="monitor_ems", target=t.monitor_ems, daemon=True).start()
```

## File: openapi.yaml/openapi.yaml
```yaml
openapi: 3.0.3
info:
  title: Aerosint API
  description: The API of the Aerosint Scarlett test bench.
  version: 3.3.0
servers:
  - url: /api/v3
tags:
  - name: Debug
    description: Endpoints used for maintenance and debugging.
paths:
  /x:
    get:
      summary: Get X axis info.
      description: Returns information about the X axis.
      operationId: get_x_info
      responses:
        "200":
          description: X axis info successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - running
                  - position
                  - homed
                properties:
                  running:
                    type: boolean
                    description: A flag that indicates if the X axis moves.
                  position:
                    type: number
                    format: double
                    minimum: 0
                    description: "The X axis position [mm]."
                    example: 12.345
                  homed:
                    type: boolean
                    description: A flag that indicates if the X axis has been homed.
        "404":
          description: Not found
          content:
            application/json:
              schema:
                type: object
                description: HTTP status code and error message returned by the server.
                required:
                  - status_code
                  - message
                properties:
                  status_code:
                    type: integer
                    format: int32
                    example: 200
                  message:
                    type: string
                    example: Error message
  /x/motion:
    get:
      summary: Get the current motion command.
      description: Returns the current motion command if there is a motion. Returns nothing otherwise.
      operationId: get_x_motion
      responses:
        "200":
          description: Motion command successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/paths/~1x~1motion/post/requestBody/content/application~1json/schema"
        "204":
          description: There is currently no motion.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
    post:
      summary: Post a motion command.
      description: Creates a motion command if possible.
      operationId: set_x_motion
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mode
                - speed
              properties:
                mode:
                  type: string
                  enum:
                    - absolute
                    - relative
                    - homing
                  example: relative
                  description: |
                    Motion's mode:
                      * absolute - Absolute movement, goes to the specified position.
                      * relative - Relative movement, travels the given distance.
                      * homing - Homing movement, returns to its reference position.
                speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The speed of the motion [mm/s]."
                  example: 30
                distance:
                  type: number
                  format: double
                  description: "The absolute or relative distance of the motion [mm]."
                  example: 100
      responses:
        "201":
          description: Motion command created.
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/paths/~1x/get/responses/404/content/application~1json/schema"
        "404":
          $ref: "#/paths/~1x/get/responses/404"
        "409":
          description: Conflict
          content:
            application/json:
              schema:
                $ref: "#/paths/~1x/get/responses/404/content/application~1json/schema"
    delete:
      summary: Delete the current motion command.
      description: Cancels and removes the current motion command.
      operationId: cancel_x_motion
      responses:
        "204":
          description: Motion command cancelled.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /z:
    get:
      summary: Get Z axis info.
      description: Returns information about the Z axis.
      operationId: get_z_info
      responses:
        "200":
          description: Z axis info successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - running
                  - position
                  - homed
                properties:
                  running:
                    type: boolean
                    description: A flag that indicates if the Z axis moves.
                  position:
                    type: number
                    format: double
                    minimum: 0
                    description: "The Z axis position [mm]."
                    example: 12.345
                  homed:
                    type: boolean
                    description: A flag that indicates if the Z axis has been homed.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /z/motion:
    get:
      summary: Get the current motion command.
      description: Returns the current motion command if there is a motion. Returns nothing otherwise.
      operationId: get_z_motion
      responses:
        "200":
          description: Motion command successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/paths/~1z~1motion/post/requestBody/content/application~1json/schema"
        "204":
          description: There is currently no motion.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
    post:
      summary: Post a motion command.
      description: Creates a motion command if possible.
      operationId: set_z_motion
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mode
                - speed
              properties:
                mode:
                  type: string
                  enum:
                    - absolute
                    - relative
                    - homing
                  example: relative
                  description: |
                    Motion's mode:
                      * absolute - Absolute movement, goes to the specified position.
                      * relative - Relative movement, travels the given distance.
                      * homing - Homing movement, returns to its reference position.
                speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The speed of the motion [mm/s]."
                  example: 5
                distance:
                  type: number
                  format: double
                  description: "The absolute or relative distance of the motion [mm]."
                  example: 100
      responses:
        "201":
          description: Motion command created.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
        "404":
          $ref: "#/paths/~1x/get/responses/404"
        "409":
          $ref: "#/paths/~1x~1motion/post/responses/409"
    delete:
      summary: Delete the current motion command.
      description: Cancels and removes the current motion command.
      operationId: cancel_z_motion
      responses:
        "204":
          description: Motion command cancelled.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /z/gripper:
    get:
      summary: Get the gripper state.
      description: Returns the current state of the gripper.
      operationId: get_gripper_state
      responses:
        "200":
          description: Gripper state successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/paths/~1z~1gripper/put/requestBody/content/application~1json/schema"
    put:
      summary: Set the gripper state.
      description: Defines the state of the gripper.
      operationId: put_gripper_state
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - state
              properties:
                state:
                  type: boolean
                  description: The state of the gripper.
      responses:
        "204":
          description: Gripper state successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
  /print/parameters:
    get:
      summary: Get print's parameters.
      description: Returns the current parameters of the print.
      operationId: get_print_parameters
      responses:
        "200":
          description: Layer parameters successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - filling_id
                  - patterning_speed
                properties:
                  filling_id:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/filling_id"
                  patterning_speed:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/patterning_speed"
                  x_offset:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/x_offset"
                  max_x_offset:
                    type: number
                    format: double
                    description: "The maximum offset along the X axis [mm]."
                    example: 100
                    minimum: 0
                  travel_speed:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/travel_speed"
                  z_speed:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/z_speed"
                  z_offset:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/z_offset"
                  layer_thickness:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/layer_thickness"
                  collectors_delay:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/collectors_delay"
                  powder_saving:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/powder_saving"
                  layer_start:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/layer_start"
                  layer_end:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/layer_end"
    put:
      summary: Set layer's parameters.
      description: Defines the parameters of the current layer.
      operationId: put_print_parameters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - filling_id
                - patterning_speed
              properties:
                filling_id:
                  type: integer
                  format: int32
                  description: "The ID of the drum with the filling material powder. If there should not be any filling, set this index to -1."
                  example: 1
                patterning_speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The patterning speed [mm/s]."
                  example: 30
                x_offset:
                  type: number
                  format: double
                  description: "The offset along the X axis [mm]."
                  example: 0
                  minimum: 0
                travel_speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The travel speed [mm/s]."
                  example: 30
                z_speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The patterning speed [mm/s]."
                  example: 30
                z_offset:
                  type: number
                  format: double
                  description: "The offset along the Z axis [mm]."
                  example: 0
                  minimum: 0
                layer_thickness:
                  type: number
                  format: double
                  description: "The layer thickness [mm]."
                  example: 0
                  minimum: 0
                collectors_delay:
                  type: integer
                  format: int32
                  description: The number of layer before emptying the collectors.
                  example: 10
                powder_saving:
                  type: boolean
                  description: A flag indicating if the powder saving strategies are used or not.
                  default: true
                layer_start:
                  type: integer
                  format: int32
                  description: The first layer to print.
                  example: 1
                layer_end:
                  type: integer
                  format: int32
                  description: The last layer to print.
                  example: 5
      responses:
        "204":
          description: Layer parameters successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
  /print/job:
    post:
      summary: Post a print job.
      description: Creates a printing job if the server is ready to start. The recoater starts the printing procedure and waits for the synchro signal.
      operationId: start_print_job
      responses:
        "202":
          description: Print job successfully created.
        "409":
          $ref: "#/paths/~1x~1motion/post/responses/409"
    delete:
      summary: Delete the current print job.
      description: Cancels and removes the current printing job.
      operationId: cancel_print_job
      responses:
        "204":
          description: Print job successfully cancelled.
  /print/info:
    get:
      summary: Get print info.
      description: Returns information about the print job.
      operationId: get_print_info
      responses:
        "200":
          description: Print info successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - n_layers
                  - last_layer
                properties:
                  n_layers:
                    type: integer
                    format: int32
                    minimum: 0
                  last_layer:
                    type: integer
                    format: int32
                    nullable: true
                    minimum: 0
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /preview:
    get:
      summary: Get specified layer preview.
      description: Returns a PNG image preview of the layer. The image is a representation of the powder allocation.
      operationId: get_preview
      parameters:
        - name: layer_id
          in: query
          description: The index of the layer to preview.
          schema:
            type: integer
            format: int32
            minimum: 0
            default: 0
      responses:
        "200":
          description: Layer preview retrieved.
          content:
            image/png:
              schema:
                type: string
                format: binary
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  "/geometries/{drum_id}":
    put:
      summary: Set the drum full geometry.
      description: Defines the full geometry of the specified drum.
      operationId: put_drum_geometry
      parameters:
        - name: drum_id
          in: path
          description: The drum's ID.
          required: true
          schema:
            type: integer
            format: int32
            minimum: 0
      requestBody:
        required: true
        content:
          application/octet-stream:
            schema:
              type: string
              format: binary
      responses:
        "204":
          description: Drum full geometry successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
    delete:
      summary: Delete the drum geometry.
      description: Removes the full geometry of the specified drum.
      operationId: delete_drum_geometry
      parameters:
        - name: drum_id
          in: path
          description: The drum's ID.
          required: true
          schema:
            type: integer
            format: int32
            minimum: 0
      responses:
        "204":
          description: Drum geometry successfully deleted.
  /state:
    get:
      summary: Get the server's state.
      description: Returns the current state of the server.
      operationId: get_state
      responses:
        "200":
          description: Server state successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - state
                properties:
                  state:
                    type: string
                    enum:
                      - ready
                      - printing
                      - waiting_for_reset
                      - error
                    example: ready
                    description: |
                      Server's state:
                        * ready - The server is waiting requests.
                        * printing - The server is printing a layer.
                        * waiting_for_reset - The server needs the reset button to be pushed
                        * error - The server has errors.
  /light:
    get:
      summary: Get the light state.
      description: Returns the current state of the light.
      operationId: get_light
      responses:
        "200":
          description: Light state successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - state
                properties:
                  state:
                    type: boolean
                    example: true
                    description: |
                      Light state:
                        * true - the light is turned on
                        * false - the light is turned off.
    put:
      summary: Set the light state.
      description: Returns the current state of the light.
      operationId: set_light
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - state
              properties:
                state:
                  type: boolean
                  description: |
                    Light state:
                      * true - the light is turned on
                      * false - the light is turned off.
      responses:
        "204":
          description: Light state successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
```

## File: tasks.py/tasks.py
```python
"""Module defining functions used by the server of the Scarlett test bench.
It defines the printing procedure and a cancel procedure that will be run on
cancel event.
"""
import time
from multiprocessing import Queue
from threading import Thread
import requests
from alibrary.motions import MotionType
from alibrary.motions.nanotec import NanotecMotionCommand
from alibrary import logger
from alibrary.server import CustomHttpError
from . import models as m
from . import config
class CancellationException(Exception):
    """Raised when print procedure should cancel"""
def print_procedure(queue: Queue):
    """Executes the printing procedure of the Scarlett test bench.
    It uses a Queue to send to the main process the errors it encountered.
    Args:
        queue: The error queue between the processes
    """
    try:
        base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"
        layer_range = range(m.printer.start_layer_index,
                            m.printer.end_layer_index + 1)
        for crt_layer in layer_range:
            # Move z down
            z_motion_down = NanotecMotionCommand(
                motion_type=MotionType.RELATIVE,
                speed=m.z_axis.speed,
                distance=-m.z_axis.offset)
            m.z_axis.start_motion(z_motion_down)
            Thread(target=m.axis_position.moving).start()
            while m.z_axis.motor.is_busy():
                time.sleep(0.1)
            # Move head to the right
            x_motion_right = NanotecMotionCommand(
                motion_type=MotionType.ABSOLUTE,
                speed=m.x_axis.travel_speed,
                distance=config.X_RIGHT_PRINT_POSITION)
            m.x_axis.start_motion(x_motion_right)
            while m.x_axis.motor.is_busy():
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)
            # Move z up
            z_motion_up = NanotecMotionCommand(motion_type=MotionType.RELATIVE,
                                               speed=m.z_axis.speed,
                                               distance=m.z_axis.offset -
                                               m.z_axis.layer_thickness)
            m.z_axis.start_motion(z_motion_up)
            Thread(target=m.axis_position.moving).start()
            while m.z_axis.motor.is_busy():
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)
            # Put drums geometries
            for d in range(config.N_DRUMS):
                layer = m.printer.get_layer_for_drum(crt_layer, d)
                requests.put(
                    f"{base_url}/drums/{d}/geometry",
                    data=layer,
                    headers={"Content-Type": "application/octet-stream"},
                    timeout=10.0)
            # Start print job
            requests.post(f"{base_url}/print/job", timeout=3.0)
            time.sleep(1)
            # Move head to the left
            x_motion_left = NanotecMotionCommand(
                motion_type=MotionType.ABSOLUTE,
                speed=m.x_axis.patterning_speed,
                distance=config.X_LEFT_PRINT_POSITION)
            m.x_axis.start_motion(x_motion_left)
            while m.x_axis.motor.is_busy():
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)
            # While printing, sleep
            response = requests.get(f"{base_url}/state", timeout=3.0)
            while response.json()["state"] == "printing":
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)
                response = requests.get(f"{base_url}/state", timeout=3.0)
            # Move head to left limit to trigger collector
            if (m.printer.collectors_delay != 0 and
                    crt_layer % m.printer.collectors_delay == 0):
                x_motion_left = NanotecMotionCommand(
                    motion_type=MotionType.ABSOLUTE,
                    speed=m.x_axis.patterning_speed,
                    distance=config.X_MIN_POSITION)
                m.x_axis.start_motion(x_motion_left)
                while m.x_axis.motor.is_busy():
                    if should_cancel():
                        raise CancellationException()
                    time.sleep(0.1)
            time.sleep(1)
            with m.last_printed_layer_index.get_lock():
                m.last_printed_layer_index.value = crt_layer
    except CustomHttpError as error:
        logger.error("Error during print procedure: %s", str(error))
        queue.put(error)
    except CancellationException:
        print("Cancel required")
        cancel_procedure()
def cancel_procedure():
    """Executes the cancel procedure."""
    m.x_axis.stop_motion()
    base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"
    requests.delete(f"{base_url}/print/job", timeout=3.0)
def get_preview(layer_id):
    """Get the preview of the specified layer"""
    base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"
    # Put drums geometries
    for d in range(config.N_DRUMS):
        layer = m.printer.get_layer_for_drum(layer_id, d)
        if layer is not None:
            requests.put(f"{base_url}/drums/{d}/geometry",
                         data=layer,
                         headers={"Content-Type": "application/octet-stream"},
                         timeout=10.0)
        else:
            requests.delete(f"{base_url}/drums/{d}/geometry", timeout=3.0)
    return requests.get(f"{base_url}/layer/preview",
                        headers={
                            "Content-Type": "image/png"
                        },
                        timeout=3.0).content
def reset():
    """Resets the y driver."""
    logger.info("Resetting the driver of y axis.")
    m.z_driver.initialization_sequence()
    with m.Z_AXIS_POSITION.get_lock():
        position = m.Z_AXIS_POSITION.value
        if position != -1:
            m.z_driver.perform_position_homing(int(position * 1000))
def monitor_door():
    """Watches the door interlock signal"""
    while m.SHOULD_LOOP:
        if not m.controllino.is_door_interlock_closed(
        ) and not m.WAS_DOOR_OPEN.is_set():
            logger.warning("Door interlock opened")
            m.z_driver.client.close()
            m.WAS_DOOR_OPEN.set()
        elif m.controllino.is_door_interlock_closed(
        ) and m.WAS_DOOR_OPEN.is_set():
            time.sleep(7)
            m.z_driver.connect()
            reset()
            time.sleep(1)
            m.WAS_DOOR_OPEN.clear()
        time.sleep(0.2)
def monitor_cover():
    """Watches the cover interlock signal"""
    m.WAS_COVER_OPEN.clear()
    while m.SHOULD_LOOP:
        if not m.controllino.is_cover_interlock_closed(
        ) and not m.WAS_COVER_OPEN.is_set():
            logger.warning("Cover interlock opened")
            m.WAS_COVER_OPEN.set()
        elif m.controllino.is_cover_interlock_closed(
        ) and m.WAS_COVER_OPEN.is_set():
            m.WAS_COVER_OPEN.clear()
        time.sleep(0.2)
def monitor_ems():
    """Watches the EMS signal"""
    m.WAS_EMS_ACTIVATED.clear()
    while m.SHOULD_LOOP:
        if not m.controllino.is_ems_deactivated(
        ) and not m.WAS_EMS_ACTIVATED.is_set():
            logger.warning("EMS activated")
            m.WAS_EMS_ACTIVATED.set()
        time.sleep(0.2)
def should_cancel():
    return m.WAS_EMS_ACTIVATED.is_set() or m.WAS_COVER_OPEN.is_set(
    ) or m.WAS_DOOR_OPEN.is_set()
```

## File: z_axis_position.py/z_axis_position.py
```python
"""
File containing the class saving the position of an axis.
"""
from time import sleep
from alibrary.electronics.nanotec import NanotecDriverError
from alibrary import logger
from . import models as m
class ZAxisPosition:
    """
    Class monitoring the position of an axis.
    """
    def moving(self):
        """
        Waits for an axis to stop to retrieve its position.
        """
        try:
            with m.Z_AXIS_POSITION.get_lock():
                m.Z_AXIS_POSITION.value = -1
                self.wait_finished()
                sleep(1)
                m.Z_AXIS_POSITION.value = m.z_axis.motor.get_position()
        except NanotecDriverError as e:
            logger.warning(str(e))
    def wait_finished(self):
        """
        Waits for the axis to stop moving.
        """
        running = True
        while running:
            if (m.controllino.is_door_interlock_closed() and
                    m.controllino.is_ems_deactivated()):
                running = m.z_axis.motor.is_busy()
                sleep(0.1)
            elif not m.controllino.is_door_interlock_closed():
                m.WAS_DOOR_OPEN.set()
                raise NanotecDriverError("Door interlock opened")
            elif not m.controllino.is_ems_deactivated():
                m.WAS_EMS_ACTIVATED.set()
                raise NanotecDriverError("EMS activated")
```
