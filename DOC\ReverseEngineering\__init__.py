"""Entry point of the Connexion server.

This file defines the factory method that build the Flask app.
"""
import os
from importlib.metadata import PackageNotFoundError, version

import yaml

try:
    __version__ = version(__name__)
except PackageNotFoundError:
    __version__ = "0.0.1"

api_file = os.path.dirname(__file__) + "/openapi.yaml"
with open(api_file, "r", encoding="utf-8") as fh:
    API_VERSION = yaml.safe_load(fh)["info"]["version"]

LOGO = r"""
    ___                        _       __
   /   | ___  _________  _____(_)___  / /_
  / /| |/ _ \/ ___/ __ \/ ___/ / __ \/ __/
 / ___ /  __/ /  / /_/ (__  ) / / / / /_
/_/  |_\___/_/   \____/____/_/_/ /_/\__/"""

COLORED_LOGO = "\x1b[36;20m" + LOGO + "\x1b[0m"
