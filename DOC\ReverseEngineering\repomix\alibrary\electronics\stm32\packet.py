"""Module defining a custom format packet used to communicate the depositions
matrices to the Controllino. It contains the matrices along some metadata that
will be send in a header.
"""
from dataclasses import dataclass

import numpy as np


@dataclass
class STM32Packet:
    """A custom format packet to communicate the deposition matrices to the
    STM32.

    One packet can hold the deposition matrix of multiple drums.
    """
    pixel_size: int
    speed: float
    offset: float = 0
    data: np.ndarray | None = None

    @property
    def line_duration(self) -> int:
        """The time between two pixel lines in µs."""
        return int(1 / (self.speed * 1000 / self.pixel_size) * 1000000)

    @property
    def time_before_ejection(self) -> int:
        """The time to wait between the synchro signal and the start of the
        ejection.

        This i the x_offset in microseconds.
        """
        # return round(self.offset * 1000 / self.pixel_size)
        return int(self.offset / self.speed * 1000000)

    @property
    def n_bytes(self) -> int:
        """The number of bytes inside the data matrix of this packet."""
        if self.data is not None:
            return self.data.size
        return 0

    def __concatenate_depositions(self, d1: np.ndarray, d2: np.ndar<PERSON>,
                                  offset: float) -> np.ndarray:
        """Concatenate two deposition matrices.

        The matrices are shifted by the given offset.

        Args:
            d1: The first deposition matrix
            d2: The second deposition matrix
            offset: The offset between the two deposition matrices [mm]

        Returns:
            A matrix with the result of the concatenation
        """
        offset_rows = round(offset * 1000 / self.pixel_size)

        if d1.shape[0] < d2.shape[0]:
            diff = d2.shape[0] - d1.shape[0]
            d1 = np.pad(d1, ((0, diff), (0, 0)))
        elif d2.shape[0] < d1.shape[0]:
            diff = d1.shape[0] - d2.shape[0]
            d2 = np.pad(d2, ((diff, 0), (0, 0)))

        d1o = np.pad(d1, ((0, offset_rows), (0, 0)))
        d2o = np.pad(d2, ((offset_rows, 0), (0, 0)))

        return np.concatenate((d1o, d2o), axis=1)

    @staticmethod
    def __shift_data(data: np.ndarray):
        """Shifts the given matrix to fit the valves layout."""

        shifted_data: np.ndarray = np.pad(data, ((0, 22), (0, 0)))

        # Shift every other pixel by 2 pixels
        shifted_data[:, 0::2] = np.roll(shifted_data[:, 0::2], 2, axis=0)

        # Shift every other 64 pixels block by 20 pixels
        for i in range(1, shifted_data.shape[1] // 64, 2):
            shifted_data[:, i * 64:(i + 1) * 64] = np.roll(
                shifted_data[:, i * 64:(i + 1) * 64], 21, axis=0)

        return shifted_data.astype(int)

    def build_data(self, depositions: np.ndarray, gaps: list[float]):
        """Constructs the payload of this packet.

        The `data` and `n_bytes` fields will be computed and filled based on
        the given depositions matrices and gap. This can manage both single and
        double depositions.

        Args:
            depositions: An ndarray containing the depositions to send to the
            Controllino
            gap: A float describing the gap between the depositions
        """
        if depositions.ndim != 3:
            raise ValueError("Depositions sHould have 3 dimensions")

        if len(gaps) != depositions.shape[0] - 1:
            n_matrices = depositions.shape[0]
            n_gaps = len(gaps)
            raise ValueError(
                f"Received {n_matrices} deposition matrices but {n_gaps} gaps")

        data = np.flip(np.transpose(depositions, (0, 2, 1)), axis=(1, 2))
        result = data[0]
        for gap_index, gap in enumerate(gaps):
            drum_index = gap_index + 1
            result = self.__concatenate_depositions(result, data[drum_index],
                                                    gap)

        # Compensate valves shifts
        data = self.__shift_data(result)

        # Convert to bytes
        self.data = np.packbits(data, axis=1, bitorder="little")
