"""Module describing a pressure regulator for the ejection pressure of the
recoater.
"""
from alibrary.pneumatic.pressure_regulator import PressureRegulator
from alibrary.electronics.stm32 import STM32, STM32Error
from alibrary.server import InternalServerError
from alibrary.logger import logger


class PiezoEjectionPressureRegulator(PressureRegulator):
    """Implementation of PressureRegulator for the ejection pressure inside a
    a recoater with tubes.

    Attributes:
        stm32: A STM32 object
        drum_id: An integer representing the drum of which to modify the
        ejection
    """

    def __init__(self, stm32: STM32, drum_id: int, maximum: float) -> None:
        super().__init__(maximum)
        self.stm32: STM32 = stm32
        self.drum_id: int = drum_id

    @property
    def pressure(self) -> float:
        """Getter for the current pressure."""
        return self.stm32.get_ejection_pressure(self.drum_id)

    @PressureRegulator.target.setter
    def target(self, pressure: float) -> None:
        """Setter for the pressure target."""
        try:
            self.stm32.set_ejection_pressure(self.drum_id, pressure)
            self._target = pressure
        except STM32Error as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
