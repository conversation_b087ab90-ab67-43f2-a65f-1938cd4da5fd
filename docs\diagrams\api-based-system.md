# Existing API-Based Recoater Control System

This diagram shows the existing REST API system that provides an alternative control path, bypassing some Controllino functionality for direct hardware access.

```mermaid
graph TB
    subgraph "External Client Applications"
        CLIENT[External Client<br/>Python/Web/Custom App]
        WEB[Web Interface<br/>Flutter App]
    end
    
    subgraph "API Server System"
        API[REST API Server<br/>Flask + Connexion<br/>127.0.0.1:8080]
        OPENAPI[OpenAPI Specification<br/>Aerosint API v3.3.0]
    end
    
    subgraph "Internal System Components"
        MODELS[Models Layer<br/>System State Management]
        CONTROLLERS[Controllers Layer<br/>API Endpoint Handlers]
        TASKS[Tasks Layer<br/>Print Procedures]
    end
    
    subgraph "Hardware Abstraction Layer"
        ALIBRARY[Alibrary Framework<br/>Hardware Abstraction]
        XAXIS[X-Axis Control<br/>NanotecMotor]
        ZAXIS[Z-Axis Control<br/>NanotecMotor + Gripper]
        PRINTER[Printer Model<br/>3-Drum Management]
        EXECUTOR[Procedure Executor<br/>Print Job Management]
    end
    
    subgraph "Direct Hardware Access"
        NANO_X_API[Nanotec X Driver<br/>************:502<br/>Direct Modbus]
        NANO_Z_API[Nanotec Z Driver<br/>************:502<br/>Direct Modbus]
        CTRL_API[Controllino PLC<br/>***********:4080<br/>Direct Socket]
    end
    
    subgraph "Physical Hardware"
        MOTOR_X_API[X-Axis Motor]
        MOTOR_Z_API[Z-Axis Motor]
        GRIPPER_API[Z-Axis Gripper]
        DRUMS_API[3-Drum System<br/>Valves & Pressure]
        SAFETY_API[Safety Systems<br/>EMS, Interlocks]
    end
    
    %% API Connections
    CLIENT -->|HTTP REST| API
    WEB -->|HTTP REST| API
    API -->|OpenAPI Spec| OPENAPI
    
    %% Internal Architecture
    API -->|Route Handlers| CONTROLLERS
    CONTROLLERS -->|Business Logic| MODELS
    CONTROLLERS -->|Print Operations| TASKS
    MODELS -->|Hardware Control| ALIBRARY
    
    %% Hardware Abstraction
    ALIBRARY -->|Motor Control| XAXIS
    ALIBRARY -->|Motor Control| ZAXIS
    ALIBRARY -->|Print Management| PRINTER
    ALIBRARY -->|Job Control| EXECUTOR
    
    %% Direct Hardware Communication
    XAXIS -->|Modbus TCP| NANO_X_API
    ZAXIS -->|Modbus TCP| NANO_Z_API
    ALIBRARY -->|TCP Socket| CTRL_API
    
    %% Physical Control
    NANO_X_API -->|Control Signals| MOTOR_X_API
    NANO_Z_API -->|Control Signals| MOTOR_Z_API
    NANO_Z_API -->|Control Signals| GRIPPER_API
    CTRL_API -->|I/O Control| DRUMS_API
    CTRL_API -->|Safety Monitoring| SAFETY_API
    
    %% API Endpoints (Key Features)
    subgraph "Key API Endpoints"
        EP1["/x/motion - X-axis control"]
        EP2["/z/motion - Z-axis control"]
        EP3["/z/gripper - Gripper control"]
        EP4["/geometries/{drum_id} - Drum setup"]
        EP5["/print/job - Print management"]
        EP6["/state - System status"]
        EP7["/print/parameters - Print config"]
    end
    
    API -.->|Implements| EP1
    API -.->|Implements| EP2
    API -.->|Implements| EP3
    API -.->|Implements| EP4
    API -.->|Implements| EP5
    API -.->|Implements| EP6
    API -.->|Implements| EP7
    
    %% Styling
    classDef apiLayer fill:#e3f2fd
    classDef clientLayer fill:#f1f8e9
    classDef hardwareLayer fill:#fce4ec
    classDef abstractionLayer fill:#fff3e0
    classDef endpointLayer fill:#f3e5f5
    
    class CLIENT,WEB clientLayer
    class API,OPENAPI,CONTROLLERS,MODELS,TASKS apiLayer
    class ALIBRARY,XAXIS,ZAXIS,PRINTER,EXECUTOR abstractionLayer
    class NANO_X_API,NANO_Z_API,CTRL_API,MOTOR_X_API,MOTOR_Z_API,GRIPPER_API,DRUMS_API,SAFETY_API hardwareLayer
    class EP1,EP2,EP3,EP4,EP5,EP6,EP7 endpointLayer
```

## Key Features

### REST API Server
- **Flask + Connexion**: Modern Python web framework with OpenAPI integration
- **Port**: 127.0.0.1:8080 (configurable for external access)
- **OpenAPI 3.0**: Comprehensive API specification (Aerosint API v3.3.0)

### Hardware Abstraction
- **Alibrary Framework**: Custom hardware abstraction layer
- **Direct Protocol Access**: Bypasses Controllino for motor control
- **Modbus TCP**: Direct communication with Nanotec motor drivers
- **Socket Communication**: Direct TCP communication with Controllino for drum control

### Key API Endpoints
- **Motion Control**: `/x/motion`, `/z/motion` for axis control
- **Gripper Control**: `/z/gripper` for Z-axis gripper
- **Drum Management**: `/geometries/{drum_id}` for drum setup
- **Print Jobs**: `/print/job` for print management
- **System Status**: `/state` for overall system status
- **Configuration**: `/print/parameters` for print parameters
