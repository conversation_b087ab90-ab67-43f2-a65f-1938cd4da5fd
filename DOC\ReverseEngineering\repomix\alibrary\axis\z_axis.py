"""Module defining an Z axis, a subclass of the abstract Axis class."""
from alibrary.axis.axis import Axis

from alibrary.axis.gripper import Gripper
from alibrary.motions.abstract.motor import Motor


class ZAxis(Axis):
    """A Z axis extending the abstract class Axis and defining some specific
    parameters.

    Attributes:
        speed: The motion speed used during procedures.
        offset: The distance that the axis descends when the head passes over.
        layer_thickness: The thickness of the printed layer.
    """
    def __init__(
        self,
        motor: Motor,
        speed: float,
        offset: float,
        layer_thickness: float,
        gripper: Gripper
    ) -> None:
        super().__init__(motor)
        self.speed = speed
        self.offset = offset
        self.layer_thickness = layer_thickness
        self.gripper = gripper
