"""Module defining a Gripper class."""
from alibrary.electronics.controllino.controllino import Controllino
from alibrary.logger import logger


class Gripper:
    """Gripper class that uses the given controllino to modify the state of the
    gripper.
    """

    def __init__(self, controllino: Controllino) -> None:
        self._controllino = controllino

    def get_gripper_state(self) -> dict[str,]:
        """Returns the current state of the gripper.

        Returns:
            A JSON object describing the gripper's state
        """
        return {"state": self._controllino.get_gripper_state()}

    def set_gripper_state(self, state: dict[str,]) -> None:
        """Sets the state of the gripper.

        Args:
            A JSON object describing the gripper state
        """
        state = state["state"]
        self._controllino.set_gripper_state(state)
        logger.info("Gripper set to %s", state)
