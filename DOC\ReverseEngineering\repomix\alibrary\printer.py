"""Module defining a printer class responsible for handling multiple layers."""
import pycli
from alibrary.server import BadRequestError


class Printer:
    """Printer class responsible for handling multiple layers."""

    def __init__(self) -> None:
        self.clis: dict[int, pycli.CLI] = {}
        self.pngs: dict[int, bytes] = {}
        self.should_use_png: bool = False
        self.last_printed_layer_index: int = 0
        self.start_layer_index: int = 0
        self.end_layer_index: int = 0
        self.collectors_delay: int = 0

    def get_info(self) -> dict[str,]:
        n_layers = 0
        if self.should_use_png:
            n_layers = 1
        else:
            for cli in self.clis.values():
                if len(cli.geometry.layers) > n_layers:
                    n_layers = len(cli.geometry.layers)

        return {
            "n_layers": n_layers,
            "last_layer": self.last_printed_layer_index,
        }

    def set_drum_cli(self, drum_id: int, cli_file: bytes):
        try:
            self.clis[drum_id] = pycli.parse(cli_file)
            self.should_use_png = False
            self.last_printed_layer_index = 0
        except pycli.ParsingError as error:
            raise BadRequestError(
                f"Error with CLI file: {str(error)}") from error

    def set_drum_png(self, drum_id: int, png_file: bytes):
        self.last_printed_layer_index = 0
        self.pngs[drum_id] = png_file
        self.should_use_png = True

    def get_layer_for_drum(self, layer_id: int, drum_id: int):
        if self.should_use_png and drum_id in self.pngs:
            return self.pngs[drum_id]

        if not self.should_use_png and drum_id in self.clis:
            return self.clis[drum_id].sub_cli(layer_id, layer_id +
                                              1).to_ascii().encode("ascii")

        return None

    def remove_geometry(self, drum_id: int):
        self.clis.pop(drum_id, None)
        self.pngs.pop(drum_id, None)
