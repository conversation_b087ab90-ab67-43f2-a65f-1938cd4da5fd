# Directory Structure
```
__init__.py/
  __init__.py
axis/
  __init__.py/
    __init__.py
  axis.py/
    axis.py
  gripper.py/
    gripper.py
  x_axis.py/
    x_axis.py
  y_axis.py/
    y_axis.py
  z_axis.py/
    z_axis.py
electronics/
  __init__.py/
    __init__.py
  controllino/
    __init__.py/
      __init__.py
    controllino.py/
      controllino.py
    packet.py/
      packet.py
    parameters.py/
      parameters.py
    plc.py/
      plc.py
    register.py/
      register.py
  ethernet.py/
    ethernet.py
  modbus.py/
    modbus.py
  nanotec/
    __init__.py/
      __init__.py
    driver.py/
      driver.py
    state.py/
      state.py
  pcb.py/
    pcb.py
  rexroth.py/
    rexroth.py
  stm32/
    __init__.py/
      __init__.py
    packet.py/
      packet.py
    state.py/
      state.py
    stm32.py/
      stm32.py
logger.py/
  logger.py
motions/
  __init__.py/
    __init__.py
  abstract/
    __init__.py/
      __init__.py
    command.py/
      command.py
    motor.py/
      motor.py
  nanotec/
    __init__.py/
      __init__.py
    command.py/
      command.py
    motor.py/
      motor.py
  pcb/
    __init__.py/
      __init__.py
    command.py/
      command.py
    motor.py/
      motor.py
  rexroth/
    __init__.py/
      __init__.py
    command.py/
      command.py
    motor.py/
      motor.py
pneumatic/
  __init__.py/
    __init__.py
  hood_valve.py/
    hood_valve.py
  piezo_ejection_regulator.py/
    piezo_ejection_regulator.py
  pressure_regulator.py/
    pressure_regulator.py
  tubes_ejection_regulator.py/
    tubes_ejection_regulator.py
  valve.py/
    valve.py
print/
  __init__.py/
    __init__.py
  parameters.py/
    parameters.py
printer.py/
  printer.py
recoater/
  __init__.py/
    __init__.py
  bridge_breakers.py/
    bridge_breakers.py
  config.py/
    config.py
  drums/
    __init__.py/
      __init__.py
    blade.py/
      blade.py
    config.py/
      config.py
    decorators/
      __init__.py/
        __init__.py
      blade.py/
        blade.py
      collector.py/
        collector.py
      decorator.py/
        decorator.py
    drum.py/
      drum.py
    drums.py/
      drums.py
    interface.py/
      interface.py
  executor.py/
    executor.py
  layer/
    __init__.py/
      __init__.py
    layer.py/
      layer.py
    parameters.py/
      parameters.py
  leveler.py/
    leveler.py
  shovels.py/
    shovels.py
server.py/
  server.py
```

# Files

## File: __init__.py/__init__.py
```python
"""Modules defining the business logic classes used in the Aerosint backends.
"""
from importlib.metadata import version
from alibrary.logger import init_logger, logger
__version__ = version("alibrary")
__all__ = ["init_logger", "logger"]
```

## File: axis/__init__.py/__init__.py
```python
"""Modules defining classes that represent printer axis.
"""
from alibrary.axis.axis import Axis
from alibrary.axis.x_axis import XAxis
from alibrary.axis.y_axis import YAxis
from alibrary.axis.z_axis import ZAxis
from alibrary.axis.gripper import Gripper
__all__ = [
    "Axis",
    "XAxis",
    "YAxis",
    "ZAxis",
    "Gripper",
]
```

## File: axis/axis.py/axis.py
```python
"""Module defining a generic axis"""
from abc import ABC
from alibrary.motions.abstract.command import MotionCommand
from alibrary.motions.abstract.motor import Motor
class Axis(ABC):
    """Generic axis."""
    def __init__(self, motor: Motor) -> None:
        self.motor = motor
    def get_info(self) -> dict[str,]:
        """Returns infos about this axis.
        Returns:
            A JSON representation of the axis infos.
        """
        return self.motor.get_info()
    def get_command(self) -> dict[str,]:
        """Returns the infos of this axis motor.
        Returns:
            A JSON representation of the motor infos.
        """
        return self.motor.get_command()
    def start_motion(self, command: MotionCommand) -> None:
        """Starts a motion on this axis.
        Args:
            command: A MotionCommand object that will be send to this axis
            motor.
        """
        self.motor.start(command)
    def stop_motion(self) -> None:
        """Stops any currently running motion on this axis motor."""
        self.motor.stop()
```

## File: axis/gripper.py/gripper.py
```python
"""Module defining a Gripper class."""
from alibrary.electronics.controllino.controllino import Controllino
from alibrary.logger import logger
class Gripper:
    """Gripper class that uses the given controllino to modify the state of the
    gripper.
    """
    def __init__(self, controllino: Controllino) -> None:
        self._controllino = controllino
    def get_gripper_state(self) -> dict[str,]:
        """Returns the current state of the gripper.
        Returns:
            A JSON object describing the gripper's state
        """
        return {"state": self._controllino.get_gripper_state()}
    def set_gripper_state(self, state: dict[str,]) -> None:
        """Sets the state of the gripper.
        Args:
            A JSON object describing the gripper state
        """
        state = state["state"]
        self._controllino.set_gripper_state(state)
        logger.info("Gripper set to %s", state)
```

## File: axis/x_axis.py/x_axis.py
```python
"""Module defining an X axis, a subclass of the abstract Axis class."""
from alibrary.axis.axis import Axis
from alibrary.motions.abstract.motor import Motor
class XAxis(Axis):
    def __init__(
        self,
        motor: Motor,
        patterning_speed: float,
        travel_speed: float,
    ) -> None:
        super().__init__(motor)
        self.patterning_speed = patterning_speed
        self.travel_speed = travel_speed
```

## File: axis/y_axis.py/y_axis.py
```python
"""Module defining an Y axis, a subclass of the abstract Axis class."""
from alibrary.axis.axis import Axis
class YAxis(Axis):
    pass
```

## File: axis/z_axis.py/z_axis.py
```python
"""Module defining an Z axis, a subclass of the abstract Axis class."""
from alibrary.axis.axis import Axis
from alibrary.axis.gripper import Gripper
from alibrary.motions.abstract.motor import Motor
class ZAxis(Axis):
    """A Z axis extending the abstract class Axis and defining some specific
    parameters.
    Attributes:
        speed: The motion speed used during procedures.
        offset: The distance that the axis descends when the head passes over.
        layer_thickness: The thickness of the printed layer.
    """
    def __init__(
        self,
        motor: Motor,
        speed: float,
        offset: float,
        layer_thickness: float,
        gripper: Gripper
    ) -> None:
        super().__init__(motor)
        self.speed = speed
        self.offset = offset
        self.layer_thickness = layer_thickness
        self.gripper = gripper
```

## File: electronics/__init__.py/__init__.py
```python
"""Modules defining interfaces to communicate with the electronics hardware.
It is the gateway between Python and the various PLC.
"""
from alibrary.electronics.controllino import (
    ControllinoError,
    ControllinoPacket,
    ControllinoParameters,
    ControllinoPLC,
    Controllino,
)
from alibrary.electronics.ethernet import EthernetComponent
from alibrary.electronics.modbus import ModbusComponent, ModbusError
from alibrary.electronics.nanotec import (
    NanotecDriver,
    NanotecDriverError,
    NanotecDriverState,
)
from alibrary.electronics.pcb import PssPCB, PssPCBError
__all__ = [
    "Controllino",
    "ControllinoPLC",
    "ControllinoError",
    "ControllinoPacket",
    "ControllinoParameters",
    "EthernetComponent",
    "ModbusComponent",
    "ModbusError",
    "NanotecDriver",
    "NanotecDriverError",
    "NanotecDriverState",
    "PssPCB",
    "PssPCBError",
]
```

## File: electronics/controllino/__init__.py/__init__.py
```python
"""Modules defining an interface to communicate with a Controllino PLC.
"""
from alibrary.electronics.controllino.controllino import Controllino
from alibrary.electronics.controllino.packet import ControllinoPacket
from alibrary.electronics.controllino.parameters import ControllinoParameters
from alibrary.electronics.controllino.plc import ControllinoError, ControllinoPLC
__all__ = [
    "Controllino",
    "ControllinoError",
    "ControllinoPacket",
    "ControllinoParameters",
    "ControllinoPLC",
]
```

## File: electronics/controllino/controllino.py/controllino.py
```python
"""Module defining an interface to a Controllino PLC.
It allows to send the powder deposition matrices to the PLC into a custom
format.
"""
from alibrary.electronics.controllino.packet import ControllinoPacket
from alibrary.electronics.controllino.parameters import ControllinoParameters
from alibrary.electronics.controllino.plc import ControllinoPLC
from alibrary.electronics.controllino.register import (
    EJECTION_REGISTERS,
    ELECTRICAL_BRIDGE_BREAKERS,
    POWDER_COLLECTORS_REGISTERS,
    ControllinoRegisters,
)
CtrlnParams = ControllinoParameters
class Controllino:
    """An interface above a set of Controllino to abstract their numbers.
    """
    # Safety status byte
    SAFETY_STATUS_READ_REGISTER = 0
    def __init__(self,
                 n_drums: int,
                 plcs: list[ControllinoPLC],
                 cyclone_level: int = 50,
                 pneumatic_bridge_breakers: bool = False,
                 is_piezo: bool = False) -> None:
        self.__n_drums = n_drums
        self.__plcs = plcs
        self.pneumatic_bridge_breakers = pneumatic_bridge_breakers
        self.__is_piezo = is_piezo
        self.__cyclone_level = cyclone_level
        self.__parameters: CtrlnParams = CtrlnParams.from_n_drums(n_drums)
        self.__cyclone_activation = 0
    @property
    def plcs(self):
        """Getter for the PLCs."""
        return self.__plcs
    def get_cyclone_level(self) -> int:
        """Returns the cyclone level."""
        return self.__cyclone_level
    def set_cyclone_level(self, level: int):
        """Sets the cyclone level."""
        self.set_vfd(level)
        self.__cyclone_level = level
    def get_ejection(self, drum_id: int) -> float:
        """Returns the stored ejection pressure of the given drum.
        Args:
            drum_id: The index of the drum from which to return the ejection
        Returns:
            A float representing the ejection pressure
        """
        return self.__parameters.ejection_pressures[drum_id]
    def set_ejection(self, drum_id: int, pressure: float):
        """Sets the ejection pressure of a given drum.
        It will store the value internally and then send every parameters to
        the Controllino.
        Args:
            drum_id: The index of the drum whose ejection need to be set
            pressure: The ejection pressure to set
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        self.__parameters.ejection_pressures[drum_id] = pressure
        register = EJECTION_REGISTERS[drum_id]
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=int(pressure))
    def activate_cyclone(self, index: int):
        """Activates the cyclone at 50% of its full capacity.
        It also registers who triggers the activation to allow to properly
        deactivate the cyclone.
        Args:
            index: An index identifying the element requesting the activation
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        if self.__cyclone_activation == 0:
            self.set_vfd(self.__cyclone_level)
        self.__cyclone_activation |= 2**index
    def deactivate_cyclone(self, index: int):
        """Deactivates the cyclone if all registered components have deactivate
        it.
        Args:
            index: An index identifying the element requesting the deactivation
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        self.__cyclone_activation &= ~2**index
        if self.__cyclone_activation == 0:
            self.set_vfd(0)
    def set_vfd(self, value: int):
        """Sets the value of the variable frequency drive.
        It will store the value internally and then send every parameters to
        the Controllino.
        Args:
            value: The vfd value to set
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        if self.__is_piezo:
            value = int(value / 100 * 255)
            register = ControllinoRegisters.FREQUENCY_VARIATOR_PIEZO
        else:
            value = int(value / 100 * 4095)
            register = ControllinoRegisters.FREQUENCY_VARIATOR
        self.__parameters.variable_frequency_drive = value
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=value)
    def get_bridge_breakers_state(self) -> bool:
        """Returns the stored state of the bridge breakers.
        Returns:
            A bool representing the state of the bridge breakers
        """
        return self.__parameters.bridge_breakers_state
    def set_bridge_breakers_state(self, state: bool):
        """Sets the state of the bridge breakers.
        It will store the state internally and then send every parameters to
        the Controllino.
        Args:
            state: The bridge breakers state to set
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        self.__parameters.bridge_breakers_state = state
        if self.pneumatic_bridge_breakers:
            register = ControllinoRegisters.PNEUMATIC_BRIDGE_BREAKER
            self.__plcs[register.controllino_id].send_parameter(
                register=register, value=state)
        else:
            value = 1843 if state else 0
            for drum_id in range(self.__n_drums):
                register = ELECTRICAL_BRIDGE_BREAKERS[drum_id]
                self.__plcs[register.controllino_id].send_parameter(
                    register=register, value=value)
    def get_shovels_state(self) -> int:
        """Returns the stored state of the shovels.
        Returns:
            A bool representing the state of the shovels
        """
        return self.__parameters.shovels_state
    def set_shovels_state(self, state: int):
        """Sets the state of the shovels.
        It will store the state internally and then send every parameters to
        the Controllino.
        Args:
            state: The shovels state to set
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        self.__parameters.shovels_state = state
        register = ControllinoRegisters.SHOVELS
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=state)
    def get_collectors(self, drum_id: int) -> bool:
        """Returns the state of the powder collector of the given drum.
        Args:
            drum_id: The index of the drum from which to return the state
        Returns:
            A bool representing the state of the powder collector
        """
        return self.__parameters.powder_collectors_state[drum_id]
    def set_collectors(self, drum_id: int, state: bool):
        """Sets the state of the powder collector of a given drum.
        It will store the value internally and then send every parameters to
        the Controllino.
        Args:
            drum_id: The index of the drum whose powder collector need to be set
            state: The state of the powder collector to set
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        self.__parameters.powder_collectors_state[drum_id] = state
        register = POWDER_COLLECTORS_REGISTERS[drum_id]
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=state)
    def get_gripper_state(self) -> bool:
        """Returns the Z gripper state.
        Returns:
            A bool representing the state
        """
        return self.__parameters.gripper_state
    def set_gripper_state(self, state: bool):
        """Sets the state of the Z gripper.
        It will store the state internally and then send the parameter to
        the Controllino.
        Args:
            state: The gripper state to set
        Raises:
            ControllinoError: An error occurs in th communication with the
            Controllino.
        """
        self.__parameters.gripper_state = state
        register = ControllinoRegisters.GRIPPER_Z
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=state)
    def send_packet(self, index: int, packet: ControllinoPacket):
        """Sends a custom packet to the Controllino.
        Args:
            packet: A ControllinoPacket object
        """
        self.__plcs[index].send_packet(packet)
    def wait_end_of_print(self):
        """Waits for the Controllino to signal the end of the pattern."""
        for plc in self.__plcs:
            plc.wait_end_of_print()
    def cancel_print(self):
        """Cancels the current print job"""
        for plc in self.__plcs:
            plc.cancel_print()
    def __get_safety_register(self) -> bytes:
        """Reads the safety status register"""
        register = ControllinoRegisters.SAFETY_STATUS
        return self.__plcs[register.controllino_id].read_register(register)
    def is_ems_deactivated(self) -> bool:
        """Returns the safety status bit"""
        register = self.__get_safety_register()[0]
        return bool(register >> 0 & 1)
    def is_leveler_ems_deactivated(self) -> bool:
        """Returns the leveler status bit"""
        register = self.__get_safety_register()[0]
        return bool(register >> 1 & 1)
    def is_cover_interlock_closed(self) -> bool:
        """Returns the cover interlock status bit"""
        register = self.__get_safety_register()[0]
        return bool(register >> 2 & 1)
    def is_door_interlock_closed(self) -> bool:
        """Returns the door interlock status bit"""
        register = self.__get_safety_register()[0]
        return bool(register >> 3 & 1)
    def set_piezos_state(self, state: bool):
        """Powers up the piezos"""
        register = ControllinoRegisters.POWER_SUPPLY_PIEZO
        self.__parameters.power_supply_piezo_state = state
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=state)
    def get_piezos_state(self):
        return self.__parameters.power_supply_piezo_state
    def get_chamber_light_state(self):
        return self.__parameters.chamber_light_state
    def set_chamber_light_state(self, state: bool):
        """Turns on and off the chamber light"""
        register = ControllinoRegisters.CHAMBER_LIGHT
        self.__parameters.chamber_light_state = state
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=state)
    def get_mesh_cleaner_state(self):
        """Gets the state of the mesh cleaner"""
        return self.__parameters.mesh_cleaner_state
    def set_mesh_cleaner_state(self, state: bool):
        """Sets the state of the mesh cleaner"""
        register = ControllinoRegisters.MESH_CLEANER
        self.__parameters.mesh_cleaner_state = state
        self.__plcs[register.controllino_id].send_parameter(register=register,
                                                            value=state)
    # TODO: Revamp those
    # def test_one_valve(self, number_valve: int):
    #     soc = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    #     soc.connect((self.ip, self.port))
    #     soc.sendall(0x04.to_bytes(1, byteorder="big"))
    #     soc.sendall(0x00.to_bytes(1, byteorder="big"))
    #     soc.sendall(number_valve.to_bytes(2, byteorder="big"))
    #     soc.shutdown(socket.SHUT_RDWR)
    # def test_valves(self, min_nb_valve: int, max_nb_valve: int):
    #     soc = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    #     soc.connect((self.ip, self.port))
    #     soc.sendall(0x04.to_bytes(1, byteorder="big"))
    #     soc.sendall(0x04.to_bytes(1, byteorder="big"))
    #     soc.sendall(min_nb_valve.to_bytes(2, byteorder="big"))
    #     soc.sendall(max_nb_valve.to_bytes(2, byteorder="big"))
    #     soc.shutdown(socket.SHUT_RDWR)
    # def set_mode(self, number_test: int):
    #     if not number_test in (1, 2, 3):
    #         logger.error("Wrong debug mode number")
    #         return
    #     soc = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    #     soc.connect((self.ip, self.port))
    #     soc.sendall(0x04.to_bytes(1, byteorder="big"))
    #     soc.sendall(number_test.to_bytes(1, byteorder="big"))
    #     soc.shutdown(socket.SHUT_RDWR)
```

## File: electronics/controllino/packet.py/packet.py
```python
"""Module defining a custom format packet used to communicate the depositions
matrices to the Controllino. It contains the matrices along some metadata that
will be send in a header.
"""
from dataclasses import dataclass
import numpy as np
@dataclass
class ControllinoPacket:
    """A custom format packet to communicate the deposition matrices to the
    Controllino PLC.
    One packet can hold the deposition matrix of multiple drums.
    """
    pixel_size: int
    speed: float
    offset: float = 0
    data: np.ndarray | None = None
    @property
    def line_duration(self) -> int:
        """The time between two pixel lines in µs."""
        return int(1 / (self.speed * 1000 / self.pixel_size) * 1000000)
    @property
    def n_blank_lines(self) -> int:
        """The number of blank lines before the print, the x offset in pixel"""
        return round(self.offset * 1000 / self.pixel_size)
    @property
    def n_bytes(self) -> int:
        """The number of bytes inside the data matrix of this packet."""
        if self.data is not None:
            return self.data.size
        return 0
    def __concatenate_depositions(self, d1: np.ndarray, d2: np.ndarray,
                                  offset: float) -> np.ndarray:
        """Concatenate two deposition matrices.
        The matrices are shifted by the given offset.
        Args:
            d1: The first deposition matrix
            d2: The second deposition matrix
            offset: The offset between the two deposition matrices [mm]
        Returns:
            A matrix with the result of the concatenation
        """
        offset_rows = round(offset * 1000 / self.pixel_size)
        d1o = np.pad(d1, ((0, offset_rows), (0, 0)))
        d2o = np.pad(d2, ((offset_rows, 0), (0, 0)))
        return np.concatenate((d1o, d2o), axis=1)
    @staticmethod
    def __shift_data(data: np.ndarray):
        """Shifts the given matrix to fit the valves layout."""
        shifted_data: np.ndarray = np.pad(data, ((0, 12), (0, 0)))
        shifted_data[:, 0::4] = np.roll(shifted_data[:, 0::4], 0, axis=0)
        shifted_data[:, 1::4] = np.roll(shifted_data[:, 1::4], 8, axis=0)
        shifted_data[:, 2::4] = np.roll(shifted_data[:, 2::4], 4, axis=0)
        shifted_data[:, 3::4] = np.roll(shifted_data[:, 3::4], 12, axis=0)
        return shifted_data.astype(int)
    def build_data(self, depositions: np.ndarray, gap: float):
        """Constructs the payload of this packet.
        The `data` and `n_bytes` fields will be computed and filled based on
        the given depositions matrices and gap. This can manage both single and
        double depositions.
        Args:
            depositions: An ndarray containing the depositions to send to the
            Controllino
            gap: A float describing the gap between the depositions
        """
        depositions = np.squeeze(depositions)
        if depositions.ndim == 2:
            data = np.flip(np.transpose(depositions, (1, 0)), axis=(0, 1))
            offset_rows = round(gap * 1000 / self.pixel_size)
            data = np.pad(data, ((offset_rows, 0), (0, 0)))
        elif depositions.ndim == 3 and depositions.shape[0] == 2:
            data = np.flip(np.transpose(depositions, (0, 2, 1)), axis=(1, 2))
            data = self.__concatenate_depositions(data[0], data[1], gap)
        else:
            raise ValueError("Wrong dimensions")
        # Compensate valves shifts
        data = self.__shift_data(data)
        # Convert to bytes
        self.data = np.packbits(data, axis=1, bitorder="little")
```

## File: electronics/controllino/parameters.py/parameters.py
```python
"""Module defining the parameters controlled by the Controllino PLC.
In the V2, the Controllino is responsible for the valves and some other
parameters. This module defines a class that gathers those parameters.
"""
from dataclasses import dataclass
# @dataclass
# class ControllinoParameters:
#     """List of parameters controlled by the Controllino on the recoater."""
#     ejection_pressures: list[float]
#     variable_frequency_drive: int = 0
#     bridge_breakers_state: bool = False
#     @classmethod
#     def from_n_drums(cls, n_drums: int) -> "ControllinoParameters":
#         return cls(ejection_pressures=[0.0] * n_drums)
@dataclass
class ControllinoParameters:
    """List of parameters controlled by the Controllino on the recoater."""
    ejection_pressures: list[float]
    powder_collectors_state: list[bool]
    variable_frequency_drive: int = 0
    bridge_breakers_state: bool = False
    shovels_state: int = 0
    gripper_state: bool = False
    n_drums: int = 0
    power_supply_piezo_state: int = 0
    chamber_light_state: bool = False
    mesh_cleaner_state: bool = False
    @classmethod
    def from_n_drums(cls, n_drums: int) -> "ControllinoParameters":
        """Factory method building a ControllinoParameters object based on the
        given number of drums.
        """
        return cls(ejection_pressures=[0.0] * n_drums,
                   powder_collectors_state=[False] * n_drums,
                   n_drums=n_drums)
```

## File: electronics/controllino/plc.py/plc.py
```python
"""Module defining an interface to a physical Controllino PLC."""
import socket
from multiprocessing import Lock
from alibrary.electronics.controllino.packet import ControllinoPacket
from alibrary.electronics.controllino.register import ControllinoRegister
from alibrary.electronics.ethernet import EthernetComponent
from alibrary.logger import logger
class ControllinoError(Exception):
    """Exception raised when an error occurs in the communication with the
    Controllino.
    """
class ControllinoPLC(EthernetComponent):
    """Interface to a physical Controllino PLC."""
    def __init__(
        self,
        ip: str,
        port: int,
        timeout: int = 2,
        offline: bool = False,
    ) -> None:
        super().__init__(ip, port, timeout, offline)
        self.__packet_socket: socket.socket = None
        self.__test_socket: socket.socket = None
        self.lock = Lock()
    def send_parameter(self, register: ControllinoRegister, value: int):
        """Sends `value` to the `register`.
        Args:
            register:
            value:
        """
        error_cnt = 0
        status = 5
        if not self.offline:
            while error_cnt < 5 and status != 0:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as soc:
                    try:
                        with self.lock:
                            soc.connect((self.ip, self.port))
                            soc.sendall(self.__to_bytes(0x01))
                            soc.sendall(self.__to_bytes(register.register_id))
                            soc.sendall(
                                self.__to_bytes(value,
                                                n_bytes=register.n_bytes))
                        if register.ack:
                            status = int.from_bytes(soc.recv(1),
                                                    byteorder="big")
                        else:
                            status = 0
                        if register.ack:
                            self.__check_ack(status)
                    except socket.timeout as error:
                        logger.error(
                            "(Controllino) Timeout while sending a parameter")
                        raise ControllinoError(str(error)) from error
                    except socket.error as error:
                        logger.error(
                            "(Controllino) Error while sending a parameter")
                        raise ControllinoError(str(error)) from error
                error_cnt += 1
            if status != 0:
                raise ControllinoError(
                    "Error while setting Controllino parameter")
        logger.debug("(Controllino) Value %d send to register %s", value,
                     register)
    def send_packet(self, packet: ControllinoPacket):
        """Sends a custom packet to the Controllino.
        Args:
            packet: A ControllinoPacket object
        """
        if not self.offline:
            self.__packet_socket = socket.socket(socket.AF_INET,
                                                 socket.SOCK_STREAM)
            try:
                with self.lock:
                    self.__packet_socket.connect((self.ip, self.port))
                    # Control byte
                    self.__packet_socket.sendall(self.__to_bytes(0x00))
                    # Header
                    self.__packet_socket.sendall(
                        self.__to_bytes(packet.n_bytes, n_bytes=4))
                    self.__packet_socket.sendall(
                        self.__to_bytes(packet.line_duration, n_bytes=4))
                    self.__packet_socket.sendall(
                        self.__to_bytes(packet.n_blank_lines, n_bytes=4))
                    # Body
                    self.__packet_socket.sendall(packet.data)
                # data = self.__packet_socket.recv(4096)
                # value = data.decode("utf-8")
                # print("Data:", data, "Value:", value)
            except socket.timeout as error:
                logger.error(
                    "(Controllino) Connection timeout while sending a matrix")
                raise ControllinoError(str(error)) from error
            except socket.error as error:
                logger.error("(Controllino) Error while sending a matrix")
                raise ControllinoError(str(error)) from error
        logger.debug("(Controllino) Matrix sent to %s", self.ip)
    def set_test_mode(self, test_index: int):
        """Set a test mode in the Controllino"""
        if not self.offline:
            self.__test_socket = socket.socket(socket.AF_INET,
                                               socket.SOCK_STREAM)
            try:
                with self.lock:
                    self.__test_socket.connect((self.ip, self.port))
                    self.__test_socket.sendall(self.__to_bytes(0x04))
                    self.__test_socket.sendall(self.__to_bytes(test_index))
            except socket.timeout as error:
                logger.error("(Controllino) Timeout while sending a parameter")
                raise ControllinoError(str(error)) from error
            except socket.error as error:
                logger.error("(Controllino) Error while sending a parameter")
                raise ControllinoError(str(error)) from error
    def close_test_socket(self):
        """Closes the test socket"""
        if self.__test_socket is not None:
            self.__test_socket.close()
        self.__test_socket = None
    def wait_end_of_print(self):
        """Waits for the Controllino to signal the end of the pattern."""
        if not self.offline and self.__packet_socket is not None:
            print("Waiting data...")
            with self.lock:
                self.__packet_socket.recv(32)
            self.__packet_socket = None
    def read_register(self, register: ControllinoRegister) -> bytes:
        """Reads the specified register"""
        if not self.offline:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as soc:
                try:
                    with self.lock:
                        soc.connect((self.ip, self.port))
                        soc.sendall(self.__to_bytes(0x01))
                        soc.sendall(self.__to_bytes(register.register_id))
                        return soc.recv(register.n_bytes)
                except socket.timeout as error:
                    logger.error(
                        "(Controllino) Timeout while reading a register")
                    raise ControllinoError(str(error)) from error
                except socket.error as error:
                    logger.error("(Controllino) Error while reading a register")
                    raise ControllinoError(str(error)) from error
        else:
            return b"\x0F"
    def cancel_print(self):
        """Cancels the current print job"""
        if not self.offline and self.__packet_socket is not None:
            logger.debug("Closing connection to the COntrollino")
            self.__packet_socket.shutdown(socket.SHUT_RDWR)
            self.__packet_socket = None
    @staticmethod
    def __to_bytes(value: int | bool, n_bytes: int = 1) -> bytes:
        """Converts the given value into its bytes representation.
        It will return n bytes in big endian order that represent the give
        integer.
        Args:
            value: The integer to convert
            n_bytes: The number of bytes in the result
        Returns:
            A bytes object representing the value
        """
        return value.to_bytes(n_bytes, byteorder="big")
    @staticmethod
    def __check_ack(status: int):
        """Logs the state of the communication with the Controllino depending
        on the given status code.
        """
        if status == 0:
            logger.debug("(Controllino) Set parameters: Success")
        elif status == 1:
            logger.error(
                "(Controllino) Set parameters: Length to long for buffer")
        elif status == 2:
            logger.error(
                "(Controllino) Set parameters: Address send, NACK received")
        elif status == 3:
            logger.error(
                "(Controllino) Set parameters: Receive NACK on transmit of data"
            )
        elif status == 4:
            logger.error("(Controllino) Set parameters: Other error")
        elif status == 5:
            logger.error("(Controllino) Set parameters: Timeout")
```

## File: electronics/controllino/register.py/register.py
```python
"""Module defining a ControllinoRegister class and an enumeration of all defined
registers.
"""
from dataclasses import dataclass
@dataclass
class ControllinoRegister:
    """A fake register implemented in the Controllino layer.
    The parameters are isolated and consider like registers on the PLCs.
    A register is identify by a controllino id and a register id.
    For each register, this class specifies the number of bytes and if an
    acknowledgement is sent.
    """
    controllino_id: int
    register_id: int
    ack: bool
    n_bytes: int
@dataclass
class ControllinoRegisters:
    """List of Controllino registers"""
    SAFETY_STATUS = ControllinoRegister(0, 0, False, 1)
    EJECTION_PRESSURE_DRUM_1 = ControllinoRegister(0, 1, False, 1)
    EJECTION_PRESSURE_DRUM_2 = ControllinoRegister(0, 2, False, 1)
    EJECTION_PRESSURE_DRUM_3 = ControllinoRegister(1, 1, False, 1)
    FREQUENCY_VARIATOR = ControllinoRegister(0, 3, True, 2)
    SPARE = ControllinoRegister(0, 4, True, 2)
    ELECTRICAL_BRIDGE_BREAKER_1 = ControllinoRegister(0, 5, True, 2)
    ELECTRICAL_BRIDGE_BREAKER_2 = ControllinoRegister(0, 6, True, 2)
    ELECTRICAL_BRIDGE_BREAKER_3 = ControllinoRegister(1, 5, True, 2)
    POWDER_COLLECTOR_DRUM_1 = ControllinoRegister(0, 7, False, 1)
    POWDER_COLLECTOR_DRUM_2 = ControllinoRegister(0, 8, False, 1)
    POWDER_COLLECTOR_DRUM_3 = ControllinoRegister(0, 9, False, 1)
    SHOVELS = ControllinoRegister(0, 10, False, 1)
    MESH_CLEANER = ControllinoRegister(0, 11, False, 1)
    PNEUMATIC_BRIDGE_BREAKER = ControllinoRegister(0, 12, False, 1)
    GRIPPER_Z = ControllinoRegister(0, 13, False, 1)
    FREQUENCY_VARIATOR_PIEZO = ControllinoRegister(0, 14, False, 1)
    POWER_SUPPLY_PIEZO = ControllinoRegister(0, 15, False, 1)
    CHAMBER_LIGHT = ControllinoRegister(0, 17, False, 1)
EJECTION_REGISTERS = [
    ControllinoRegisters.EJECTION_PRESSURE_DRUM_1,
    ControllinoRegisters.EJECTION_PRESSURE_DRUM_2,
    ControllinoRegisters.EJECTION_PRESSURE_DRUM_3,
]
POWDER_COLLECTORS_REGISTERS = [
    ControllinoRegisters.POWDER_COLLECTOR_DRUM_1,
    ControllinoRegisters.POWDER_COLLECTOR_DRUM_2,
    ControllinoRegisters.POWDER_COLLECTOR_DRUM_3,
]
ELECTRICAL_BRIDGE_BREAKERS = [
    ControllinoRegisters.ELECTRICAL_BRIDGE_BREAKER_1,
    ControllinoRegisters.ELECTRICAL_BRIDGE_BREAKER_2,
    ControllinoRegisters.ELECTRICAL_BRIDGE_BREAKER_3,
]
```

## File: electronics/ethernet.py/ethernet.py
```python
"""Module defining a class for the electronics components that are reach via
Ethernet.
This allows to isolate Ethernet specific properties.
"""
from abc import ABC
from dataclasses import dataclass
@dataclass
class EthernetComponent(ABC):
    """Electronics component accessed by the server via Ethernet."""
    ip: str
    port: int
    timeout: int = 2
    offline: bool = False
```

## File: electronics/modbus.py/modbus.py
```python
"""Module defining an interface to a Modbus component.
It is a facade to the pymodbus library. It exposes a simplified Modbus
communication.
Typical usage example:
component = ModbusComponent()
value = component.read_register(address=1234)
"""
import struct
from multiprocessing import Lock
from pymodbus.client import ModbusTcpClient
from alibrary.electronics.ethernet import EthernetComponent
from alibrary.logger import logger
class ModbusError(Exception):
    """Exception raised by the Modbus interface when an error occurs."""
class ModbusComponent(EthernetComponent):
    """An interface to a Modbus component.
    It uses the Facade design pattern to simplify the usage of pymodbus.
    It is generic enough to allow communication to multiple PLC.
    By default, it will create a connection on port 502 with a timeout of 2
    seconds.
    """
    def __init__(self,
                 ip: str,
                 port: int = 502,
                 timeout: int = 2,
                 offline: bool = False) -> None:
        super().__init__(ip, port, timeout, offline)
        self.client = ModbusTcpClient(host=self.ip,
                                      port=self.port,
                                      timeout=self.timeout)
        self.prefix = f"(Modbus) {self.ip} |"
        self.lock = Lock()
        self.connect()
    def connect(self):
        """Connects the client if it is not already connected.
        Launches the connection with ModbusTcpClient object and tries to connect to the PLC.
        It raises an error if the connection fails.
        Raises:
            ModbusError: The connection to the Modbus component failed.
        """
        if not self.offline:
            if not self.client.is_socket_open():
                if self.client.connect():
                    logger.debug(
                        "(Modbus) Connection to the Modbus component "
                        "(%s:%d) succeeded.", self.ip, self.port)
                else:
                    raise ModbusError("Connection to the Modbus component "
                                      f"({self.ip}:{self.port}) failed.")
            else:
                logger.debug(
                    "(Modbus) Connection to the Modbus component "
                    "(%s:%d) already established.", self.ip, self.port)
    def read_coil(self, address: int) -> bool:
        """Reads and returns the value stored inside the coil at the given
        address.
        Args:
            address: The Modbus address of the coil to read
        Returns:
            The boolean value of the coil
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if not self.offline:
            with self.lock:
                response = self.client.read_coils(address)
            if response.isError():
                raise ModbusError(
                    f"{self.prefix} Error while reading coil at {address}")
            value = response.bits[0]
        else:
            value = False
        logger.debug("%s Read %s in coil at address %d", self.prefix, value,
                     address)
        return value
    def read_coils(self, address: int) -> list[bool]:
        """Reads and returns the values stored inside the coils at th given
        address.
        Args:
            address: The Modbus address of the coils to read
        Returns:
            The list of boolean values of the coils
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if not self.offline:
            with self.lock:
                response = self.client.read_coils(address)
            if response.isError():
                raise ModbusError(
                    f"{self.prefix} Error while reading coils at {address}")
            values = response.bits
        else:
            values = []
        logger.debug("%s Read %s in coils at address %d", self.prefix, values,
                     address)
        return values
    def read_register(self, address: int) -> int:
        """Reads the register at the given address and returns this value.
        Args:
            address: The Modbus address of the register to read
        Returns:
            The value read in the register
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if not self.offline:
            with self.lock:
                response = self.client.read_input_registers(address)
            if response.isError():
                raise ModbusError(f"{self.prefix} Error while reading register "
                                  f"at {address}: {response}")
            value = response.registers[0]
        else:
            value = 0
        logger.debug("%s Read %s in register at address %d", self.prefix, value,
                     address)
        return value
    def read_registers(self, address: int) -> int:
        """Reads two consecutive registers and returns the value they represent.
        32 bits numbers are stored inside two consecutive Modbus registers.
        This method allows to retrieved such numbers.
        Args:
            address: the Modbus address of the first register. It will be
            incremented by one for the second.
        Returns:
            The value stored in the two registers
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if not self.offline:
            with self.lock:
                response = self.client.read_input_registers(address, count=2)
            if response.isError():
                raise ModbusError(
                    f"{self.prefix} Error while reading registers "
                    f"at {address}(+1): {response}")
            msb_word = response.registers[0]
            lsb_word = response.registers[1]
            value = struct.unpack(">i", struct.pack(">HH", msb_word,
                                                    lsb_word))[0]
        else:
            value = 0
        logger.debug("%s Read %s in registers at addresses %d(+1)", self.prefix,
                     value, address)
        return value
    def write_coil(self, address: int, value: bool) -> None:
        """Writes a value inside the coil at the given address.
        Args:
            address: The Modbus address of the coil to write to.
            value: The value to write
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if not self.offline:
            with self.lock:
                response = self.client.write_coil(address, value)
            if response.isError():
                raise ModbusError(
                    f"{self.prefix} Error while writing coil at {address}")
        logger.debug("%s Written %s in coil at address %d", self.prefix, value,
                     address)
    def write_coils(self, address: int, values: list[bool]) -> None:
        """Writes values inside the coils at the given address.
        Args:
            address: The Modbus address of the coils to write to.
            values: The list of values to write
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if len(values) > 16:
            raise ModbusError(
                "Cannot write more than 16 coils at the same address")
        if not self.offline:
            with self.lock:
                response = self.client.write_coils(address, values)
            if response.isError():
                raise ModbusError(
                    f"{self.prefix} Error while writing coils at {address}")
        logger.debug("%s Written %s in coils at address %d", self.prefix,
                     values, address)
    def write_register(self, address: int, value: int) -> None:
        """Writes a value inside the register at the given address.
        Args:
            address: The address of the register to write to
            value: The value to write
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if not self.offline:
            with self.lock:
                response = self.client.write_registers(address, value)
            if response.isError():
                raise ModbusError(f"{self.prefix} Error while writing register "
                                  f"at {address}: {response}")
        logger.debug("%s Written %s in register at address %d", self.prefix,
                     value, address)
    def write_registers(self, address: int, value: int) -> None:
        """Writes a value into two consecutive registers.
        32 bits numbers are stored inside two consecutive Modbus registers.
        This method allows to write such numbers.
        Args:
            address: The modbus address of the first register. It will be
            incremented by one for the second.
            value: The value to write
        Raises:
            ModbusError: An error occurs in the Modbus communication
        """
        if not self.offline:
            msb_word, lsb_word = struct.unpack(">HH", struct.pack(">i", value))
            with self.lock:
                response = self.client.write_registers(address,
                                                       [msb_word, lsb_word])
            if response.isError():
                raise ModbusError(
                    f"{self.prefix} Error while writing registers "
                    f"at {address}(+1)")
        logger.debug("%s Written %s in registers at address %d(+1)",
                     self.prefix, value, address)
```

## File: electronics/nanotec/__init__.py/__init__.py
```python
"""Modules defining an interface to communicate with a STM32.
"""
from alibrary.electronics.nanotec.driver import (NanotecDriver,
                                                 NanotecDriverError)
from alibrary.electronics.nanotec.state import NanotecDriverState
__all__ = [
    "NanotecDriver",
    "NanotecDriverError",
    "NanotecDriverState",
]
```

## File: electronics/nanotec/driver.py/driver.py
```python
"""Module describing a generic Nanotec motor driver.
This will be specialized to adjust to every kind of motor we use with a Nanotec
driver.
"""
# import struct
import time
from alibrary.electronics.modbus import ModbusComponent, ModbusError
from alibrary.electronics.nanotec.state import NanotecDriverState
from alibrary.logger import logger
class NanotecDriverError(Exception):
    """Exception raised when an error occurs in the communication with the
    Nanotec driver.
    """
class NanotecDriver(ModbusComponent):
    """Generic Nanotec driver
    Interface for any Nanotec driver used by Aerosint. It implements both
    ModbusComponent and Motor classes.
    """
    # Statusword of the Nanotec driver (6041)
    STATUS_WORD_ADDRESS = 2000
    # Controlword of the Nanotec driver (6040)
    CONTROL_WORD_ADDRESS = 3000
    # Address of the information out register
    READ_INFORMATION_ADDRESS = 2014
    # Modes of Operation of the Nanotec driver (6060)
    OPERATION_MODE_READ_ADDRESS = 2002
    # Modes of Operation Display of the Nanotec driver (6061)
    OPERATION_MODE_WRITE_ADDRESS = 3002
    # Address of the target position register
    TARGET_POSITION_ADDRESS = 3006
    # Address of the target speed register
    TARGET_SPEED_ADDRESS = 3008
    # Address of the target acceleration register
    TARGET_ACCELERATION_ADDRESS = 3010
    # Address of the target deceleration register
    TARGET_DECELERATION_ADDRESS = 3012
    # Address of the current position register
    ACTUAL_POSITION_ADDRESS = 2008
    # Address of the current speed register
    ACTUAL_SPEED_ADDRESS = 2010
    # Address of the speed register when searching the zero
    SEARCH_ZERO_SPEED_ADDRESS = 3016
    # Speed when searching the zero [µm/s]
    SEARCH_ZERO_SPEED = 3000
    # Hall sensor address
    SENSOR_ADDRESS = 2012
    # Address of the homing method index
    HOMING_METHOD_ADDRESS = 3018
    # Address of the homing position
    HOMING_OFFSET_ADDRESS = 3020
    # Homing operation mode
    HOMING_MODE = 6
    # Start homing without moving control word
    START_MOTIONLESS_HOMING = 0x17
    # Default control word value
    DEFAULT_CONTROL_WORD = 0x0
    # Start motion control word
    START_MOTION_CONTROL_WORD = 0xF
    def __init__(self,
                 ip: str,
                 port: int = 502,
                 timeout: int = 2,
                 offline: bool = False) -> None:
        super().__init__(ip, port, timeout, offline)
        self.initialization_sequence()
    def initialization_sequence(self):
        """
        Carries out the initialization procedure (cancels faults, disables,
        sets to ready, switches on).
        """
        try:
            # Initialization sequence of the driver
            if not self.offline:
                if self.has_fault():
                    self.reset_fault()
                self.__set_state_switch_on_disabled()
                self.__set_state_ready_to_switch_on()
                self.__set_state_switched_on()
                logger.info("Nanotec driver (%s:%d) successfully started",
                            self.ip, self.port)
        except NanotecDriverError as error:
            logger.error("Could not initialize Nanotec driver: %s", error)
    def __get_state(self) -> NanotecDriverState:
        """Retrieves the current state of the Nanotec driver.
        Returns:
            A NanotecDriverState object
        Raises:
            NanotecDriverError: An error occurs during the reading of the
            status word.
        """
        if self.offline:
            return NanotecDriverState.SWITCHED_ON
        try:
            status_word = self.read_registers(self.STATUS_WORD_ADDRESS)
            state = NanotecDriverState.from_status_word(status_word=status_word)
            logger.debug("(Nanotec driver) Read status %s", state)
            return state
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError(str(error)) from error
    def __set_control_word(self, value: int):
        """Sets the control word of the Nanotec driver.
        Args:
            value: The value to set as the control word
        Raises:
            NanotecDriverError: An error occurs during the writing of the
            control word.
        """
        if not self.offline:
            try:
                self.write_registers(self.CONTROL_WORD_ADDRESS, value)
            except ModbusError as error:
                logger.error(str(error))
                raise NanotecDriverError(str(error)) from error
    def has_fault(self) -> bool:
        """Checks if the driver is in FAULT state.
        Returns:
            A boolean indicating if the driver is in FAULT state or not
        Raises:
            NanotecDriverError: An error occurs while getting the state.
        """
        return self.__get_state() == NanotecDriverState.FAULT
    def reset_fault(self):
        """Resets the FAULT state of the driver
        Raises:
            NanotecDriverError: An error occurs while resetting fault.
        """
        reset_fault_control_word = 0x80
        self.__set_control_word(reset_fault_control_word)
        self.__wait_for_state(NanotecDriverState.SWITCH_ON_DISABLED)
        self.__set_control_word(self.DEFAULT_CONTROL_WORD)
    def __wait_for_state(self, state: NanotecDriverState):
        """Waits until the given state is the current state of the driver.
        Args:
            state: The state to wait
        Raises:
            NanotecDriverError: An error occurs while waiting the state.
        """
        cnt = 0
        loop_time = 0.01
        while self.__get_state() != state:
            cnt += 1
            if cnt > self.timeout // loop_time:
                logger.error("Timeout waiting state %s", state)
                raise NanotecDriverError(f"Timeout waiting state {state}")
            time.sleep(loop_time)
    def __set_state_switch_on_disabled(self):
        """Sets the driver's state to SWITCH ON DISABLED.
        Raises:
            NanotecDriverError: An error occurs while setting the state.
        """
        current_state = self.__get_state()
        if current_state == NanotecDriverState.SWITCH_ON_DISABLED:
            return
        if current_state == NanotecDriverState.FAULT:
            self.reset_fault()
            return
        if current_state == NanotecDriverState.FAULT_REACTION_ACTIVE:
            raise NanotecDriverError("Could not set state SWITCH ON DISABLED")
        self.__set_control_word(self.DEFAULT_CONTROL_WORD)
        self.__wait_for_state(NanotecDriverState.SWITCH_ON_DISABLED)
    def __set_state_ready_to_switch_on(self):
        """Sets the driver's state to READY TO SWITCH ON.
        Raises:
            NanotecDriverError: An error occurs while setting the state.
        """
        request_ready_to_switch_on_state = 0x06
        current_state = self.__get_state()
        if current_state == NanotecDriverState.READY_TO_SWITCH_ON:
            return
        if current_state in (NanotecDriverState.SWITCH_ON_DISABLED,
                             NanotecDriverState.SWITCHED_ON,
                             NanotecDriverState.OPERATION_ENABLED):
            self.__set_control_word(request_ready_to_switch_on_state)
            self.__wait_for_state(NanotecDriverState.READY_TO_SWITCH_ON)
            return
        raise NanotecDriverError("Could not set state READY TO SWITCH ON")
    def __set_state_switched_on(self):
        """Sets the driver's state to SWITCHED ON.
        Raises:
            NanotecDriverError: An error occurs while setting the state.
        """
        request_switched_on_state = 0x07
        current_state = self.__get_state()
        if current_state == NanotecDriverState.SWITCHED_ON:
            return
        if current_state in (NanotecDriverState.READY_TO_SWITCH_ON,
                             NanotecDriverState.OPERATION_ENABLED):
            self.__set_control_word(request_switched_on_state)
            self.__wait_for_state(NanotecDriverState.SWITCHED_ON)
            return
        raise NanotecDriverError("Could not set state SWITCHED ON")
    def __wait_for_operation_mode(self, mode: int):
        """Waits for the operation mode to change to the specified value."""
        cnt = 0
        while self.read_registers(self.OPERATION_MODE_READ_ADDRESS) != mode:
            cnt += 1
            if cnt > 100:
                logger.error("Timeout waiting operation mode %d", mode)
                raise NanotecDriverError(
                    f"Timeout waiting operation mode {mode}")
            time.sleep(0.01)
    def __set_operation_mode(self, mode: int):
        """Sets the operation mode of the driver.
        Args:
            mode: An integer representing the mode of operation
        Raises:
            NanotecDriverError: An error occurs while setting the operation
            mode.
        """
        try:
            if not self.offline:
                self.write_registers(self.OPERATION_MODE_WRITE_ADDRESS, mode)
                self.__wait_for_operation_mode(mode)
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError(str(error)) from error
    def __get_bit(self, address: int, bit_index: int) -> bool:
        """Returns one bit from the Nanotec driver register at the given
        address.
        Args:
            address: The address of the registers to read.
            bit_index: The index of the bit to check, starting at zero
        Raises:
            NanotecDriverError: An error occurs while checking bit of status
            word.
        """
        if self.offline:
            return False
        try:
            status_word = self.read_registers(address)
            return int(status_word / 2**bit_index) % 2 == 1
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError(str(error)) from error
    def __check_state_for_move(self):
        state = self.__get_state()
        if state not in (NanotecDriverState.SWITCHED_ON,
                         NanotecDriverState.OPERATION_ENABLED):
            err_str = f"Impossible to perform Nanotec motion from state {state}"
            logger.error(err_str)
            raise NanotecDriverError(err_str)
    def __read(self, address: int) -> int:
        """Reads a register of the Nanotec driver;
        Args:
            address: The address of the register to read
        Returns:
            The 32 bits integer stored inside the register
        Raises:
            NanotecDriverError: An error occurs while reading the register
        """
        if self.offline:
            return 0
        try:
            return self.read_registers(address)
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError("READ " + str(error)) from error
    def __write(self, address: int, value: int):
        """Writes a register of the Nanotec driver;
        Args:
            address: The address of the register to write
            value: The integer value to write
        Raises:
            NanotecDriverError: An error occurs while writing the register
        """
        if not self.offline:
            try:
                self.write_registers(address, value)
            except ModbusError as error:
                logger.error(str(error))
                raise NanotecDriverError("WRITE " + str(error)) from error
    def is_busy(self) -> bool:
        """Returns the running status of the motor.
        This is stored in the first bit of the READ_INFORMATION register.
        Returns:
            True if a motion is running on the motor, false otherwise
        Raises:
            NanotecDriverError: AAn error occurs in the process
        """
        return self.__get_bit(self.READ_INFORMATION_ADDRESS, 0)
    def is_homed(self) -> bool:
        """Returns the homing status of the motor.
        This is stored in the second bit of the READ_INFORMATION register.
        Returns:
            True if a motion is homing on the motor, false otherwise
        Raises:
            NanotecDriverError: AAn error occurs in the process
        """
        return self.__get_bit(self.READ_INFORMATION_ADDRESS, 1)
    def get_position(self) -> float:
        """Returns the current position.
        Returns:
            The current position in µm
        Raises:
            NanotecDriverError: An error occurs in the process
        """
        return self.__read(self.ACTUAL_POSITION_ADDRESS)
    def get_speed(self) -> float:
        """Returns the current speed.
        Returns:
            The current speed in µm/s
        Raises:
            NanotecDriverError: An error occurs in the process
        """
        return self.__read(self.ACTUAL_SPEED_ADDRESS)
    def perform_speed_motion(self, speed: int, acceleration: int):
        """Performs a speed motion.
        Args:
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²
        Raises:
            NanotecDriverError: AAn error occurs in the process
        """
        speed_mode = 3
        self.__check_state_for_move()
        self.__write(self.TARGET_SPEED_ADDRESS, speed)
        self.__write(self.TARGET_ACCELERATION_ADDRESS, acceleration)
        self.__set_operation_mode(speed_mode)
        self.__set_control_word(self.START_MOTION_CONTROL_WORD)
    def perform_distance_motion(self,
                                distance: int,
                                speed: int,
                                acceleration: int,
                                deceleration: int,
                                is_relative=True):
        """
        Performs a distance motion.
        It can either be relative or absolute.
        Args:
            distance: The distance to travel in µm
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²
            deceleration: The acceleration in µm/s²
            is_relative: A flag indicating if the distance is relative or not
        Raises:
            NanotecDriverError: An error occurs in the process
        """
        distance_mode = 1
        self.__check_state_for_move()
        self.__write(self.TARGET_POSITION_ADDRESS, distance)
        self.__write(self.TARGET_SPEED_ADDRESS, speed)
        self.__write(self.TARGET_ACCELERATION_ADDRESS, acceleration)
        self.__write(self.TARGET_DECELERATION_ADDRESS, deceleration)
        if is_relative:
            oms = 0b111
        else:
            oms = 0b011
        control_word = oms * 16 + 0xF
        self.__set_operation_mode(distance_mode)
        self.__set_control_word(self.START_MOTION_CONTROL_WORD)
        self.__set_control_word(control_word)
        target_reached_bit_index = 12
        while not self.__get_bit(self.STATUS_WORD_ADDRESS,
                                 target_reached_bit_index):
            time.sleep(0.1)
        # Remove 5th bit
        control_word -= 16
        self.__set_control_word(control_word)
    def perform_homing(self, speed: int, acceleration: int,
                       search_zero_speed: int):
        """Performs the homing procedure.
        Args:
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²
        Raises:
            NanotecDriverError: An error occurs in the process
        """
        full_homing_method = 17
        start_homing_control_word = 0x1F
        self.__check_state_for_move()
        self.__write(self.HOMING_OFFSET_ADDRESS, 0)
        self.__write(self.HOMING_METHOD_ADDRESS, full_homing_method)
        self.__write(self.SEARCH_ZERO_SPEED_ADDRESS, search_zero_speed)
        self.__write(self.TARGET_SPEED_ADDRESS, speed)
        self.__write(self.TARGET_ACCELERATION_ADDRESS, acceleration)
        self.__set_operation_mode(self.HOMING_MODE)
        self.__set_control_word(self.START_MOTION_CONTROL_WORD)
        self.__set_control_word(start_homing_control_word)
    def perform_custom_homing(self, speed: int, acceleration: int):
        """Performs the custom homing procedure.
        The motion is started in speed mode and stopped when the sensor is
        triggered.
        Args:
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²
        Raises:
            NanotecDriverError: An error occurs in the process
        """
        self.__check_state_for_move()
        self.perform_speed_motion(speed, acceleration)
        while not self.__read(self.SENSOR_ADDRESS) == 1:
            time.sleep(0.01)
        self.stop(acceleration)
        self.__set_operation_mode(self.HOMING_MODE)
        self.__set_control_word(self.START_MOTIONLESS_HOMING)
    def perform_position_homing(self, position: int):
        """Performs a homing operation that set the actual position without
        moving.
        Args:
            position: The position to set in µm
        Raises:
            NanotecDriverError: An error occurs in the process
        """
        position_homing_method = 35
        self.__check_state_for_move()
        self.__write(self.HOMING_METHOD_ADDRESS, position_homing_method)
        self.__write(self.HOMING_OFFSET_ADDRESS, position)
        self.__set_operation_mode(self.HOMING_MODE)
        self.__set_control_word(self.START_MOTIONLESS_HOMING)
    def stop(self, deceleration: int):
        """Stops the currently running motion.
        Args:
            deceleration: The acceleration in µm/s²
        Raises:
            NanotecDriverError: An error occurs in the process
        """
        stop_control_word = 0x7
        self.__write(self.TARGET_DECELERATION_ADDRESS, deceleration)
        self.__set_control_word(stop_control_word)
    def halt(self):
        """Stops the currently running motion.
        It sends a HALT to the Nanotec driver.
        """
        halt_control_word = 0x10F
        self.__set_control_word(halt_control_word)
```

## File: electronics/nanotec/state.py/state.py
```python
"""Module describing the different states of a Nanotec driver."""
from enum import Enum, auto
class NanotecDriverState(Enum):
    """Different states of the Nanotec driver"""
    NOT_READY_TO_SWITCH_ON = auto()
    SWITCH_ON_DISABLED = auto()
    READY_TO_SWITCH_ON = auto()
    SWITCHED_ON = auto()
    OPERATION_ENABLED = auto()
    QUICK_STOP_ACTIVE = auto()
    FAULT_REACTION_ACTIVE = auto()
    FAULT = auto()
    UNKNOWN = auto()
    @classmethod
    def from_status_word(cls, status_word: int) -> "NanotecDriverState":
        """Returns the NanotecDriverState associated with the given status word.
        Args:
            status_word: An integer representing the content of the status word
            of the driver
        Returns:
            A NanotecDriverState object
        """
        nanotec_driver_state = {
            0x0: {
                0: NanotecDriverState.NOT_READY_TO_SWITCH_ON,
                1: NanotecDriverState.SWITCH_ON_DISABLED
            },
            0x1: NanotecDriverState.READY_TO_SWITCH_ON,
            0x3: NanotecDriverState.SWITCHED_ON,
            0x7: {
                1: NanotecDriverState.OPERATION_ENABLED,
                0: NanotecDriverState.QUICK_STOP_ACTIVE
            },
            0xF: NanotecDriverState.FAULT_REACTION_ACTIVE,
            0x8: NanotecDriverState.FAULT
        }
        low_four_bits = status_word & 0xF
        if low_four_bits == 0:
            sod = (status_word & 0x40) >> 6
            return cls(nanotec_driver_state[low_four_bits][sod])
        if low_four_bits == 0x7:
            qs = (status_word & 0x20) >> 5
            return cls(nanotec_driver_state[low_four_bits][qs])
        if low_four_bits in nanotec_driver_state:
            return cls(nanotec_driver_state[low_four_bits])
        return NanotecDriverState.UNKNOWN
```

## File: electronics/pcb.py/pcb.py
```python
"""Module defining an interface to the pressure sensors and steppers PCB.
"""
from math import ceil
import socket
from multiprocessing import Lock
import struct
from alibrary.electronics.ethernet import EthernetComponent
from alibrary.logger import logger
class PssPCBError(Exception):
    """Exception raised when an error occurs in the communication with the PCB.
    """
class PssPCB(EthernetComponent):
    """An interface to the pressure sensors and steppers PCB.
    """
    def __init__(
        self,
        n_sensors: int,
        ip: str,
        port: int,
        timeout: int = 2,
        offline: bool = False,
    ) -> None:
        super().__init__(ip, port, timeout, offline)
        self.n_sensors = n_sensors
        self.lock = Lock()
        self._cache = [0 for _ in range(self.n_sensors)]
        self._cache_timestamp = 0
        if not self.offline:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            try:
                self.socket.connect((self.ip, self.port))
            except socket.timeout as error:
                logger.error("(PssPCB) Connection timeout while reading (%s)",
                             error)
                raise PssPCBError(str(error)) from error
            except socket.error as error:
                logger.error("(PssPCB) Error while reading (%s)", error)
                raise PssPCBError(str(error)) from error
    def __read(self, n_bytes: int = 1, signed: bool = False) -> int:
        """Reads and returns an integer from the PCB.
        It reads `n_bytes` bytes and then converts them into an integer.
        Args:
            n_bytes: The number of bytes to read
            signed: A flag indicating if the two's complement should be used in
            the conversion
        """
        assert self.offline is False
        data = b""
        for _ in range(n_bytes):
            data += self.socket.recv(1)
        return int.from_bytes(data, byteorder="big", signed=signed)
    def __send(self, value: int, n_bytes: int = 1, signed: bool = False):
        """Sends the given integer to the PCB.
        It converts the given int to bytes using the given number of bytes and
        the signed flag. It then sends all bytes to the PCB through a socket.
        Args:
            value: The integer to send
            n_bytes: The number of bytes the integer should be converted to
            signed: A flag indicating if the two's complement should be used in
            the conversion
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        assert self.offline is False
        data = value.to_bytes(n_bytes, byteorder="big", signed=signed)
        self.socket.sendall(data)
    def __send_float(self, value):
        data = bytearray(struct.pack(">f", value))
        for i in range(4):
            self.__send(data[i])
    def __read_float(self):
        data = b""
        for _ in range(4):
            data += self.socket.recv(1)
        return struct.unpack(">f", data)[0]
    def set_config(self, configuration_number):
        """ Sets config"""
        if not self.offline:
            if configuration_number == 0:  # SPD2: 2 meshes
                nb_2130 = 9
                chip_select_number_2130 = [5, 4, 3, 2, 9, 0xA1, 0xA2, 0xA3, 0xA7]
                mux_configuration_2130 = [False] * nb_2130
                chip_select_number_429 = [8, 7, 6]
                mux_configuration_429 = [False] * 3
                IS_VALVE = [
                    False, False, True, True, False, False, True, True, False
                ]
                nb_control = 3
                min_index_sensor = 0
                max_index_sensor = 5
                index_control_stepper = [2, 6, 7]
                index_control_sensor = [2, 4, 5]
            if configuration_number == 1:  # SPD2: 3 meshes configuration
                nb_2130 = 15
                chip_select_number_2130 = [
                    1, 2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 15, 3, 0xA1, 0xA2
                ]
                mux_configuration_2130 = [True] * 12 + [False] * 3
                chip_select_number_429 = [0, 4, 8, 12, 4]
                mux_configuration_429 = [True] * 4 + [False]
                IS_VALVE = [
                    False, False, True, True, True, False, False, True, False,
                    False, True, False, False, False, False
                ]
                nb_control = 4
                min_index_sensor = 0
                max_index_sensor = 7
                index_control_stepper = [3, 4, 7, 10]
                index_control_sensor = [2, 4, 6, 7]
            elif configuration_number == 2:  # Micro configuration
                nb_2130 = 5
                chip_select_number_2130 = [5, 4, 3, 2, 9]
                mux_configuration_2130 = [False] * 5
                chip_select_number_429 = [8, 7]
                mux_configuration_429 = [False] * 2
                IS_VALVE = [True] * 5
                nb_control = 4
                min_index_sensor = 2
                max_index_sensor = 11
                index_control_stepper = [1, 2, 3, 4]
                index_control_sensor = [3, 5, 7, 9]
            self.__send(0)
            if self.__read() == 1:
                print("Arduino already configured")
                return
            NB_STEPPERS_PER_TMC429 = 3
            nb_tmc429 = ceil(nb_2130 / NB_STEPPERS_PER_TMC429)
            self.__send(nb_2130)
            data_mux_configuration_2130 = 0
            for i in range(nb_2130):
                if mux_configuration_2130[i]:
                    data_mux_configuration_2130 += 2**i
            self.__send(data_mux_configuration_2130, 2)
            for i in range(nb_2130):
                self.__send(chip_select_number_2130[i])
            self.__send(nb_tmc429)
            data_mux_configuration_429 = 0
            for i in range(nb_tmc429):
                if mux_configuration_429[i]:
                    data_mux_configuration_429 += 2**i
            self.__send(data_mux_configuration_429)
            for i in range(nb_tmc429):
                self.__send(chip_select_number_429[i])
            send_data_is_valve = 0
            for i in range(nb_2130):
                if IS_VALVE[i]:
                    send_data_is_valve += 2**i
            self.__send(send_data_is_valve, 2)
            self.__send(min_index_sensor)
            self.__send(max_index_sensor)
            self.__send(nb_control)
            for i in range(nb_control):
                self.__send(index_control_stepper[i])
                self.__send(index_control_sensor[i])
            if self.check_driver_communication():
                print("no error communication with TMC2130 and TMC429")
            else:
                print(
                    "Error when during test communication with TMC2130 and TMC429")
    def get_raw_pressures(self) -> list[int]:
        """Returns all the measured pressures.
        Returns:
            A list of float representing the pressures
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        pressures = [0 for _ in range(self.n_sensors)]
        if not self.offline:
            with self.lock:
                self.__send(1)
                # Reading
                for i in range(self.n_sensors):
                    data = self.__read(2)
                    pressures[i] = data
        logger.debug("(PCB) Reading raw pressures %s", pressures)
        return pressures
    def perform_homing(self, index: int):
        """Performs the homing of the requested component.
        Args:
            index: An index selecting the component on which to perform the
            homing
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Performing homing of %s", bin(index))
        if not self.offline:
            with self.lock:
                self.__send(2)
                self.__send(index)
    def check_homing_done(self) -> int:
        """Checks on all component if the homing has been performed.
        It returns a binary number where a 1 at a given position means that
        the corresponding component has been homed.
        Example:
            0000 1001: The first and fourth components have been homed,
            the others not
        Returns:
            A binary number
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Checking homing")
        if not self.offline:
            with self.lock:
                self.__send(3)
                return self.__read(2)
        return 65535
    def perform_distance_motion(self, stepper_index: int, target: int):
        """Start an absolute distance motion on the specified stepper.
        Args:
            stepper_index: The index of the stepper to control
            target: The absolute target distance to reach
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Performing distance motion to %s on %s", target,
                     stepper_index)
        if not self.offline:
            with self.lock:
                self.__send(4)
                # self.__send(1)
                self.__send(stepper_index)
                self.__send(target, n_bytes=4, signed=True)
    def set_actual_position(self, stepper_index: int, position: int):
        """Sets the actual position of the specified stepper.
        To avoid too many homing on the scraping blades steppers, their current
        position is saved to a file and then restored at each startup. This
        methods allows to signified to a given stepper its actual position.
        Args:
            stepper_index: The index of the stepper to calibrate
            position: The position to set in the stepper
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Setting actual position to %s on stepper %s",
                     position, stepper_index)
        if not self.offline:
            with self.lock:
                self.__send(5)
                self.__send(stepper_index)
                self.__send(position, n_bytes=4, signed=True)
    def start_pressure_control(self, control_index: int, stepper_index: int, sensor_index:int, data: int):
        """Starts the pressure control to maintain the requested pressure in
        the given component.
        The index allows to choose the destination of this command.
        Args:
            control_index: The destination of this command
            data: The raw pressure to set
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Starting pressure control of %s for %s", data,
                     control_index)
        if not self.offline:
            with self.lock:
                self.__send(6)
                self.__send(control_index)
                self.__send(stepper_index)
                self.__send(sensor_index)
                self.__send(data, n_bytes=2)
    def stop_pressure_control(self, control_index: int):
        """Stops the pressure control of the given component
        Args:
            control_index: The destination of this command
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Stopping pressure control for %s", control_index)
        if not self.offline:
            with self.lock:
                self.__send(7)
                self.__send(control_index)
    def check_driver_communication(self):
        self.__send(8)
        communication_429 = self.__read()
        communication_2130 = self.__read(2)
        logger.warning(communication_429)
        logger.warning(communication_2130)
    def get_actual_position(self, stepper_index: int) -> int:
        """Returns the actual position of the specified stepper.
        Args:
        Returns:
        """
        if not self.offline:
            self.__send(9)
            self.__send(stepper_index)
            return self.__read(4, signed=True)
        return 0
    def check_busy(self) -> int:
        """Checks on all steppers if they are running or not.
        It returns a binary number where a 1 at a given position means that
        the corresponding stepper is busy.
        Example:
            0000 1001: The first and fourth steppers are busy, the others not
        Returns:
            A binary number
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        logger.debug("(PCB) Checking busy")
        if not self.offline:
            with self.lock:
                self.__send(10)
                return self.__read(2)
        return 65535
    def set_feedback_gains(self, control_index, proportional_gain, integral_gain):
        """Sets the integral gain of the given controlled valve.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(11)
                self.__send(control_index)
                self.__send(proportional_gain, n_bytes=4, signed=True)
                self.__send(integral_gain, n_bytes=4, signed=True)
    # 250
    def set_rms_position(self, current):
        """Sets the RMS current when the valve makes a position motion.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(14)
                self.__send(current, n_bytes=2)
    # 200
    def set_rms_control(self, control_index, current):
        """Sets the RMS current when the given valve is regulating.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(15)
                self.__send(control_index)
                self.__send(current, n_bytes=2)
    # def set_regulating_valve(self, index):
    #     """Sets if the leveler pressure is regulated using the leveler valve or
    #     the hood valve.
    #     Args:
    #     """
    #     if not self.offline:
    #         with self.lock:
    #             self.__send(16)
    #             self.__send(index)
    def set_model_parameter(self, index, model_parameter):
        """Sets the model parameter of the control number index for adaptive
        feedback.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(17)
                self.__send(index)
                self.__send_float(model_parameter)
    def get_model_parameter(self, index):
        """Returns the model parameter of the control number index for adaptive
        feedback.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(18)
                self.__send(index)
                return self.__read_float()
    def enable_printing(self):
        """Enables printing mode.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(19)
    def disable_printing(self):
        """Disables printing mode.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(20)
    def enable_adaptive_feedback(self, control_id):
        """Enables printing mode.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(21)
                self.__send(control_id)
    def disable_adaptive_feedback(self, control_id):
        """Enables printing mode.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(22)
                self.__send(control_id)
    def set_coef(self, control_index, proportional_coef, integral_coef):
        """Set coefficients for adaptive feedback.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(23)
                self.__send(control_index)
                self.__send_float(proportional_coef)
                self.__send_float(integral_coef)
    def get_feedback_gains(self, control_index:int):
        """get feedback gains.
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(24)
                self.__send(control_index)
                return self.__read_float(), self.__read_float()
        return 0, 0
    def set_model_estimation_parameters(self, update_rate, period_update_mode,
                                        time_for_model_estimation, time_for_pressure_to_stabilize):
        """Set the parameters for model estimation..
        Args:
        """
        if not self.offline:
            with self.lock:
                self.__send(25)
                self.__send_float(update_rate)
                self.__send(period_update_mode, 4)
                self.__send(time_for_model_estimation, 4)
                self.__send(time_for_pressure_to_stabilize, 4)
    def set_sensor_data_deviation(self, control_number, pressure_deviation):
        """Set the sensor data deviation.
        Args:
        """
        data_deviation = self.compute_diff_data(pressure_deviation, 1034, 0)
        print(data_deviation)
        if not self.offline:
            with self.lock:
                self.__send(34)
                self.__send(control_number)
                self.__send(data_deviation, 4)
    def set_max_feedback_gains(self, control_id, max_kp, max_ki):
        if not self.offline:
            with self.lock:
                self.__send(35)
                self.__send(control_id)
                self.__send(max_kp, 4)
                self.__send(max_ki, 4)
    @staticmethod
    def compute_diff_data(delta_pressure: float, pmax:int, pmin:int):
        NMAX = 16384
        return int( ( (0.8 * NMAX) / (pmax - pmin) ) * (delta_pressure) )
    # def perform_distance_motion_new(self, stepper_index, position):
    #     """Send values and then check if the ones read are the same."""
    #     logger.warning(self.check_actual_position(stepper_index))
    #     if not self.offline:
    #         with self.lock:
    #             self.__send(19)
    #             self.__send(stepper_index)
    #             self.__send(position, n_bytes=4, signed=True)
    #             pcb_stepper_index = self.__read()
    #             pcb_position = self.__read(n_bytes=4, signed=True)
    #             if (pcb_stepper_index != stepper_index or
    #                     pcb_position != position):
    #                 logger.warning(
    #                     "(PCB) Error in communication. Send '%s' and '%s' but\
    #                         received '%s' and '%s'", stepper_index, position,
    #                     pcb_stepper_index, pcb_position)
    #             else:
    #                 logger.debug("Communication checked")
    #     logger.warning(self.check_actual_position(stepper_index))
    # FIXME: Duplicate method with get_actual_position
    # def check_actual_position(self, stepper_index):
    #     """Reads the current position."""
    #     if not self.offline:
    #         with self.lock:
    #             self.__send(20)
    #             self.__send(stepper_index)
    #             position = self.__read(n_bytes=4, signed=True)
    #             return position
    #     return 0.0
```

## File: electronics/rexroth.py/rexroth.py
```python
"""Module defining a Rexroth driver class using the DLL and .NET framework."""
from alibrary.electronics.ethernet import EthernetComponent
from alibrary.logger import logger
class RexrothError(Exception):
    """Exception raised when an error occurs with the Rexroth driver.
    """
class RexrothDotNetDriver(EthernetComponent):
    """Interface to a Rexroth driver."""
    ACCELERATION = 50
    JERK = 0
    def __init__(
        self,
        ip: str,
        port: int,
        timeout: int = 2,
        offline: bool = False,
    ) -> None:
        super().__init__(ip, port, timeout, offline)
        self.connection = None
    def connect(self):
        """Connects to the Rexroth driver"""
        # pylint: disable=C0415
        import clr
        # pylint: disable=E1101
        clr.AddReference("../lib/EAL")
        # pylint: disable=E0401, C0413
        import EAL  # type: ignore
        if not self.offline:
            self.connection = EAL.EALConnection.EALConnection()
            self.connection.Connect(self.ip)
            axis = self.connection.Motion.Axes[0]
            axis.SetCondition(EAL.Enums.AxisCondition.AXIS_CONDITION_ACTIVE)
            axis.Movement.Power(True)
    def get_position(self) -> float:
        """Returns the current position registered inside the Rexroth driver."""
        # pylint: disable=E0602
        if not self.offline:
            self.connect()
            try:
                position = self.connection.Parameter.ReadData("S-0-0051.0.0")
            except EAL.Exceptions.EALException as error:  # type: ignore
                raise RexrothError(str(error)) from error
            self.close()
        else:
            position = 0
        logger.debug("(Rexroth) Current position retrieved, position = %s",
                     position)
        return position
    def perform_relative_motion(self, distance: float, speed: float):
        """Performs a relative motion"""
        # pylint: disable=E0602
        if not self.offline:
            self.connect()
            try:
                axis = self.connection.Motion.Axes[0]
                axis.Movement.MoveAdditive(distance, speed * 60,
                                           self.ACCELERATION, self.ACCELERATION,
                                           self.JERK)
                axis.Movement.Wait(100000)
            except EAL.Exceptions.EALException as error:  # type: ignore
                raise RexrothError(str(error)) from error
            self.close()
        logger.debug("(Rexroth) Perform relative motion to %s at %s mm/s",
                     distance, speed)
    def perform_absolute_motion(self, distance: float, speed: float):
        """Performs a absolute motion"""
        # pylint: disable=E0602
        if not self.offline:
            self.connect()
            try:
                axis = self.connection.Motion.Axes[0]
                axis.Movement.MoveAbsolute(distance, speed * 60,
                                           self.ACCELERATION, self.ACCELERATION,
                                           self.JERK)
                axis.Movement.Wait(100000)
            except EAL.Exceptions.EALException as error:  # type: ignore
                raise RexrothError(str(error)) from error
            self.close()
        logger.debug("(Rexroth) Perform relative motion to %s at %s mm/s",
                     distance, speed)
    def check_busy(self) -> bool:
        """Checks if the axis is busy or not."""
        # pylint: disable=E0602
        if not self.offline:
            self.connect()
            try:
                state = self.connection.Parameter.ReadData("S-0-0331.0.0")
                state = not bool(state)
            except EAL.Exceptions.EALException as error:  # type: ignore
                raise RexrothError(str(error)) from error
            self.close()
        else:
            state = False
        logger.debug("(Rexroth) Busy state retrieved, state = %s", state)
        return state
    def close(self):
        """Closes the connection with the Rexroth driver."""
        axis = self.connection.Motion.Axes[0]
        axis.Movement.Power(False)
        self.connection.Disconnect()
```

## File: electronics/stm32/__init__.py/__init__.py
```python
"""Modules defining an interface to communicate with a STM32.
"""
from alibrary.electronics.stm32.stm32 import STM32Error, STM32
from alibrary.electronics.stm32.packet import STM32Packet
from alibrary.electronics.stm32.state import STM32State
__all__ = [
    "STM32Packet",
    "STM32Error",
    "STM32",
    "STM32State"
]
```

## File: electronics/stm32/packet.py/packet.py
```python
"""Module defining a custom format packet used to communicate the depositions
matrices to the Controllino. It contains the matrices along some metadata that
will be send in a header.
"""
from dataclasses import dataclass
import numpy as np
@dataclass
class STM32Packet:
    """A custom format packet to communicate the deposition matrices to the
    STM32.
    One packet can hold the deposition matrix of multiple drums.
    """
    pixel_size: int
    speed: float
    offset: float = 0
    data: np.ndarray | None = None
    @property
    def line_duration(self) -> int:
        """The time between two pixel lines in µs."""
        return int(1 / (self.speed * 1000 / self.pixel_size) * 1000000)
    @property
    def time_before_ejection(self) -> int:
        """The time to wait between the synchro signal and the start of the
        ejection.
        This i the x_offset in microseconds.
        """
        # return round(self.offset * 1000 / self.pixel_size)
        return int(self.offset / self.speed * 1000000)
    @property
    def n_bytes(self) -> int:
        """The number of bytes inside the data matrix of this packet."""
        if self.data is not None:
            return self.data.size
        return 0
    def __concatenate_depositions(self, d1: np.ndarray, d2: np.ndarray,
                                  offset: float) -> np.ndarray:
        """Concatenate two deposition matrices.
        The matrices are shifted by the given offset.
        Args:
            d1: The first deposition matrix
            d2: The second deposition matrix
            offset: The offset between the two deposition matrices [mm]
        Returns:
            A matrix with the result of the concatenation
        """
        offset_rows = round(offset * 1000 / self.pixel_size)
        if d1.shape[0] < d2.shape[0]:
            diff = d2.shape[0] - d1.shape[0]
            d1 = np.pad(d1, ((0, diff), (0, 0)))
        elif d2.shape[0] < d1.shape[0]:
            diff = d1.shape[0] - d2.shape[0]
            d2 = np.pad(d2, ((diff, 0), (0, 0)))
        d1o = np.pad(d1, ((0, offset_rows), (0, 0)))
        d2o = np.pad(d2, ((offset_rows, 0), (0, 0)))
        return np.concatenate((d1o, d2o), axis=1)
    @staticmethod
    def __shift_data(data: np.ndarray):
        """Shifts the given matrix to fit the valves layout."""
        shifted_data: np.ndarray = np.pad(data, ((0, 22), (0, 0)))
        # Shift every other pixel by 2 pixels
        shifted_data[:, 0::2] = np.roll(shifted_data[:, 0::2], 2, axis=0)
        # Shift every other 64 pixels block by 20 pixels
        for i in range(1, shifted_data.shape[1] // 64, 2):
            shifted_data[:, i * 64:(i + 1) * 64] = np.roll(
                shifted_data[:, i * 64:(i + 1) * 64], 21, axis=0)
        return shifted_data.astype(int)
    def build_data(self, depositions: np.ndarray, gaps: list[float]):
        """Constructs the payload of this packet.
        The `data` and `n_bytes` fields will be computed and filled based on
        the given depositions matrices and gap. This can manage both single and
        double depositions.
        Args:
            depositions: An ndarray containing the depositions to send to the
            Controllino
            gap: A float describing the gap between the depositions
        """
        if depositions.ndim != 3:
            raise ValueError("Depositions sHould have 3 dimensions")
        if len(gaps) != depositions.shape[0] - 1:
            n_matrices = depositions.shape[0]
            n_gaps = len(gaps)
            raise ValueError(
                f"Received {n_matrices} deposition matrices but {n_gaps} gaps")
        data = np.flip(np.transpose(depositions, (0, 2, 1)), axis=(1, 2))
        result = data[0]
        for gap_index, gap in enumerate(gaps):
            drum_index = gap_index + 1
            result = self.__concatenate_depositions(result, data[drum_index],
                                                    gap)
        # Compensate valves shifts
        data = self.__shift_data(result)
        # Convert to bytes
        self.data = np.packbits(data, axis=1, bitorder="little")
```

## File: electronics/stm32/state.py/state.py
```python
"""Module describing the different states of a STM32 driver."""
from enum import Enum
class STM32State(Enum):
    """Different states of the Nanotec driver"""
    IDLE = 0
    WAITING_FOR_TRIGGER = 1
    WAITING_FOR_PRINT_START = 2
    PRINTING = 3
    TEST_MODE = 4
    WAITING_FOR_FALLING_TRIGGER = 5
    ERROR = 255
```

## File: electronics/stm32/stm32.py/stm32.py
```python
"""Module defining an interface to a physical STM32 PLC."""
import socket
import struct
import time
from multiprocessing import Lock
from alibrary.electronics.stm32.packet import STM32Packet
from alibrary.electronics.stm32.state import STM32State
from alibrary.electronics.ethernet import EthernetComponent
from alibrary.logger import logger
class STM32Error(Exception):
    """Exception raised when an error occurs in the communication with the
    STM32.
    """
class STM32(EthernetComponent):
    """Interface to a physical STM32."""
    def __init__(
        self,
        ip: str,
        port: int,
        timeout: int = 2,
        offline: bool = False,
    ) -> None:
        super().__init__(ip, port, timeout, offline)
        self.lock = Lock()
        if not self.offline:
            # Communication socket
            self.__communication_socket = socket.socket(socket.AF_INET,
                                                        socket.SOCK_STREAM)
            self.__communication_socket.settimeout(self.timeout)
            try:
                self.__communication_socket.connect((self.ip, self.port))
                self.__send(self.__communication_socket, 0)
                self.__read(self.__communication_socket)
            except socket.timeout as error:
                logger.error(
                    "(STM32) Timeout while opening communication socket")
                raise STM32Error(str(error)) from error
            except socket.error as error:
                logger.error("(STM32) Error while opening communication socket")
                raise STM32Error(str(error)) from error
            # Communication socket
            self.__printing_socket = socket.socket(socket.AF_INET,
                                                   socket.SOCK_STREAM)
            self.__printing_socket.settimeout(20)
            try:
                self.__printing_socket.connect((self.ip, self.port))
                self.__send(self.__printing_socket, 1)
                self.__read(self.__printing_socket)
            except socket.timeout as error:
                logger.error("(STM32) Timeout while opening printing socket")
                raise STM32Error(str(error)) from error
            except socket.error as error:
                logger.error("(STM32) Error while opening printing socket")
                raise STM32Error(str(error)) from error
    def __send(self,
               soc: socket.socket,
               value: int,
               n_bytes: int = 1,
               signed=False) -> None:
        """Sends the given value in its bytes representation to the given socket.
        Args:
            value: The integer to convert
            n_bytes: The number of bytes in the result
        Returns:
            A bytes object representing the value
        """
        soc.sendall(value.to_bytes(n_bytes, byteorder="little", signed=signed))
    def __send_float(self, soc: socket.socket, value: float):
        """Sends a float to the given socket"""
        soc.sendall(bytearray(struct.pack("f", value)))
    def __read(self, soc: socket.socket, n_bytes: int = 1, signed=False) -> int:
        """Reads a given number of bytes on the given socket."""
        data = b""
        for _ in range(n_bytes):
            data += soc.recv(1)
        return int.from_bytes(data, byteorder="little", signed=signed)
    def __read_float(self, soc: socket.socket):
        """Reads a float on the given socket."""
        data = b""
        for _ in range(4):
            data += soc.recv(1)
        return struct.unpack("f", data)[0]
    def get_driver_mode(self) -> STM32State:
        """Returns the current driver mode."""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 7)
                    state_received = False
                    while not state_received:
                        try:
                            state = self.__read(self.__communication_socket)
                            state_received = True
                        except socket.timeout:
                            time.sleep(1)
                    return STM32State(state)
                except ValueError as error:
                    logger.error("(STM32) State %d does not exist", state)
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error
        return 0
    def is_printing(self) -> bool:
        """Returns True if the current driver mode is printing."""
        return self.get_driver_mode() != STM32State.IDLE
    def get_ejection_pressure(self, index: int) -> int:
        """Sets the ejection pressure for the given drum.
        Args:
            index: The index of the drum for which to set the ejection
        """
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 2)
                    self.__send(self.__communication_socket, index)
                    return self.__read(self.__communication_socket, n_bytes=2)
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error
        return 0
    def set_ejection_pressure(self, index: int, pressure: int) -> None:
        """Sets the ejection pressure for the given drum.
        Args:
            index: The index of the drum for which to set the ejection
            pressure: The ejection pressure to set in Pa
        """
        enable_ejection_pressure_code = 11
        if not self.offline:
            with self.lock:
                try:
                    # Set pressure
                    self.__send(self.__communication_socket, 1)
                    self.__send(self.__communication_socket, index)
                    self.__send(self.__communication_socket,
                                pressure,
                                n_bytes=2)
                    # Enable Ejection Pressure
                    self.__send(self.__communication_socket,
                                enable_ejection_pressure_code)
                    self.__send(self.__communication_socket, index)
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error
    def disable_ejection_pressure(self, index: int) -> None:
        """Disable ejection pressure.
        """
        disable_ejection_pressure = 12
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket,
                                disable_ejection_pressure)
                    self.__send(self.__communication_socket, index)
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error
    def send_packet(self, packet: STM32Packet):
        """Sends a custom packet to the STM32.
        Args:
            packet: A STM32Packet object
        """
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__printing_socket, 0)
                    # Header
                    self.__send(self.__printing_socket, packet.n_bytes, 4)
                    self.__send(self.__printing_socket, packet.line_duration, 4)
                    self.__send(self.__printing_socket,
                                packet.time_before_ejection, 4)
                    # Body
                    self.__printing_socket.sendall(packet.data)
                    # Check image is saved
                    status = self.__read(self.__printing_socket)
                    if status != 0:
                        raise STM32Error(
                            "Problem while saving the image in the STM32 memory"
                        )
                except socket.timeout as error:
                    logger.error(
                        "(STM32) Connection timeout while sending a matrix")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while sending a matrix")
                    raise STM32Error(str(error)) from error
        logger.debug("(STM32) Matrix sent to %s", self.ip)
    def set_test_mode(self, test_index: int, period=1000):
        """Set a test mode in the STM32"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 4)
                    self.__send(self.__communication_socket, test_index)
                    self.__send(self.__communication_socket, period, n_bytes=2)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting test mode")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting test mode")
                    raise STM32Error(str(error)) from error
        logger.debug("(STM32) Test mode %s activated", test_index)
    def test_one_valve(self, valve_index: int, period=1000):
        """Set a test mode in the STM32"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 5)
                    self.__send(self.__communication_socket,
                                valve_index,
                                n_bytes=2)
                    self.__send(self.__communication_socket, period, n_bytes=2)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting test mode")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting test mode")
                    raise STM32Error(str(error)) from error
        logger.debug("(STM32) Test valve %s", valve_index)
    def test_valves(self, start_valve: int, end_valve: int, period=1000):
        """Set a test mode in the STM32"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 6)
                    self.__send(self.__communication_socket,
                                start_valve,
                                n_bytes=2)
                    self.__send(self.__communication_socket,
                                end_valve,
                                n_bytes=2)
                    self.__send(self.__communication_socket, period, n_bytes=2)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting test mode")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting test mode")
                    raise STM32Error(str(error)) from error
        logger.debug("(STM32) Test valves [%s, %s]", start_valve, end_valve)
    def stop(self):
        """Stops the ejection
        """
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 3)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while stopping ejection")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while stopping ejection")
                    raise STM32Error(str(error)) from error
        logger.debug("(STM32) STM32 stopped")
    def wait_end_of_print(self):
        """Waits for the STM32 to signal the end of the pattern."""
        while self.is_printing():
            print(self.get_driver_mode())
            time.sleep(1)
    def set_feedback_gain(self, drum_index: int, kp, ki, kd):
        """Sets the PID parameters"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 8)
                    self.__send(self.__communication_socket, drum_index)
                    self.__send_float(self.__communication_socket, kp)
                    self.__send_float(self.__communication_socket, ki)
                    self.__send_float(self.__communication_socket, kd)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting feedback gain")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting feedback gain")
                    raise STM32Error(str(error)) from error
        logger.debug(
            "(STM32) PID parameters (kp=%s, ki=%s, kd=%s) set for drum %s", kp,
            ki, kd, drum_index)
    def set_period_pid(self, period_pid):
        """Sets the period PID"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 9)
                    self.__send(self.__communication_socket,
                                period_pid,
                                n_bytes=4)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting period pid")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting period pid")
                    raise STM32Error(str(error)) from error
        logger.debug("(STM32) Period PID set to %s", period_pid)
    def get_actuation_value(self, drum_index):
        """Sets the period PID"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 10)
                    self.__send(self.__communication_socket, drum_index)
                    self.__read_float(self.__communication_socket)
                except socket.timeout as error:
                    logger.error(
                        "(STM32) Timeout while getting actuation value")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while getting actuation value")
                    raise STM32Error(str(error)) from error
        logger.debug("(STM32) Actuation value retrieved for drum %s",
                     drum_index)
    def close(self):
        """Closes the communication socket."""
        self.__communication_socket.close()
    def shutdown(self):
        """Shutdowns the communication socket."""
        self.__communication_socket.shutdown()
```

## File: logger.py/logger.py
```python
"""Aerosint logger
Custom logger using the built-in logging package.
It allows to log the requests made to the server and custom execution messages.
It also has different flavors depending on if it is a debug mode or not.
"""
import logging
import os
import time
from logging import Formatter
from logging.config import dictConfig
from flask import Flask, Response, g, request
is_debug_active = bool(os.environ.get("FLASK_DEBUG"))
class ColoredFormatter(Formatter):
    """Custom logger formatter.
    It will colorize the logging output according to the logging level.
    """
    grey = "\x1b[37;20m"
    yellow = "\x1b[33;20m"
    red = "\x1b[31;20m"
    blue = "\x1b[34;20m"
    magenta = "\x1b[35;20m"
    cyan = "\x1b[36;20m"
    bold_red = "\x1b[31;1m"
    reset = "\x1b[0m"
    log_format = "[{asctime}] {levelname:>8}: {message}"
    FORMATS = {
        logging.DEBUG: blue + log_format + reset,
        logging.INFO: grey + log_format + reset,
        logging.WARNING: yellow + log_format + reset,
        logging.ERROR: red + log_format + reset,
        logging.CRITICAL: bold_red + log_format + reset
    }
    def format(self, record):
        """Format the specified record as text.
        It picks the color according to the log level and then uses the { style
        format string to generate the log text.
        Args:
            record: A LogRecord to format
        """
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt, style="{")
        return formatter.format(record)
def config_logger(debug: bool = False):
    """Configures the aerosint logger.
    The given debug flag defines the log level.
    """
    dictConfig({
        "version": 1,
        "disable_existing_loggers": True,
        "formatters": {
            "console": {
                "()": ColoredFormatter,
            },
            "text_file": {
                "()": ColoredFormatter,
            },
        },
        "handlers": {
            "aerosint_console": {
                "level": "DEBUG",
                "class": "logging.StreamHandler",
                "formatter": "console",
                "stream": "ext://sys.stdout",
            },
            "aerosint": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "text_file",
                "filename": "aerosint.log",
                "maxBytes": 10485760,  # 10 Mb = 10 * 1024 * 1024
                "backupCount": 10,
            },
        },
        "loggers": {
            "aerosint": {
                "level": "INFO" if not debug else "DEBUG",
                "propagate": False,
                "handlers": ["aerosint"] if not debug else ["aerosint_console"]
            },
        }
    })
def start_handler_timer():
    """Stores the current time in a Flask session variable"""
    if "start" not in g:
        g.start = time.time()
def log_request(response: Response) -> Response:
    """Logs a request from its response.
    Args:
        response: The Response that is about to be return to the client.
    Returns:
        The Response object that it receive.
    """
    now = time.time()
    handling_duration = round(now - g.start, 3)
    logging.getLogger("aerosint").debug(
        "Request %-7s %-50s - %d (handled in %06.3fs)", request.method,
        request.path, response.status_code, handling_duration)
    return response
def init_logger(app: Flask):
    """Initializes the custom logger inside the Flask application."""
    app.before_request(start_handler_timer)
    app.after_request(log_request)
    log = logging.getLogger("werkzeug")
    log.disabled = True
config_logger(is_debug_active)
logger = logging.getLogger("aerosint")
```

## File: motions/__init__.py/__init__.py
```python
"""Modules defining the classes that handles the different motions in the
machine.
"""
from alibrary.motions.abstract.command import MotionCommand, MotionType
from alibrary.motions.abstract.motor import Motor
__all__ = [
    "MotionCommand",
    "MotionType",
    "Motor",
]
```

## File: motions/abstract/__init__.py/__init__.py
```python
"""Package defining an abstract motion command and an abstract motor.
Every motor used in the machine need to implement those classes. This allows
to have a common interface for every actuator.
"""
```

## File: motions/abstract/command.py/command.py
```python
"""Modules defining an abstract motion command.
This can be used to start a motion on a motor.
To handle the different kind of motors that we might use in the machines, an
abstract class is used. This allows to have a template and to benefit from OOP
advantages for the motors and motions.
"""
from abc import ABC, abstractmethod
from enum import Enum, auto
class MotionType(Enum):
    """Types of motion"""
    ABSOLUTE = auto()
    RELATIVE = auto()
    TURNS = auto()
    HOMING = auto()
    SPEED = auto()
class MotionCommand(ABC):
    """Abstract class representing a motion command.
    This command can be given to a Motor to start a motion.
    """
    def __init__(self,
                 motion_type: MotionType = MotionType.RELATIVE,
                 speed: float = 0.0,
                 distance: float = 0.0,
                 turns: float = 0.0) -> None:
        self.motion_type = motion_type
        self.speed = speed
        self.distance = distance
        self.turns = turns
    @classmethod
    @abstractmethod
    def from_json(cls, json: dict[str,]) -> "MotionCommand":
        """Deserializes a JSON object.
        This method returns a MotionCommand object based on the given JSON.
        Args:
            json: The JSON object to deserialize
        Returns:
            A MotionCommand object
        """
    @abstractmethod
    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this MotionCommand.
        Returns:
            A JSON dictionary
        """
```

## File: motions/abstract/motor.py/motor.py
```python
"""Modules defining an abstract motor class.
This receives motion command and communicate with the underlying PLC.
To handle the different kind of motors that we might use in the machines, an
abstract class is used. This allows to have a template and to benefit from OOP
advantages for the motors and motions.
"""
import math
from abc import ABC, abstractmethod
from alibrary.motions.abstract.command import MotionCommand, MotionType
from alibrary.server import BadRequestError, ConflictError
class Motor(ABC):
    """Abstract class representing an motor.
    This class should represent every kind of motors that could be used in the
    machine. It takes MotionCommand to update its parameters and communicate
    with the underlying hardware.
    """
    current_command: MotionCommand | None = None
    @abstractmethod
    def is_busy(self) -> bool:
        """Checks if this motor is busy, i.e. if there is a running motion.
        Returns:
            A bool representing the running status
        Raises:
            InternalServerError: An error occurs in the process
        """
    @abstractmethod
    def get_position(self) -> float:
        """Returns the current position
        Returns:
            A float representing the current position
        Raises:
            InternalServerError: An error occurs in the process
        """
    @abstractmethod
    def is_homed(self) -> bool:
        """Checks if this motor has been homed
        Returns:
            A bool representing the homing status
        Raises:
            InternalServerError: An error occurs in the process
        """
    def validate_command(self, command: MotionCommand, min_abs_distance: float,
                         max_abs_distance: float):
        """Checks if the command is valid regarding to the motor current state
        and parameters.
        Raises:
            BadRequestError: The given command is not valid
            InternalServerError: An error occurs in the process
        """
        # Distance must be in the good range
        if command.motion_type == MotionType.RELATIVE:
            crt_position = self.get_position()
            min_distance = min_abs_distance - crt_position
            max_distance = max_abs_distance - crt_position
        elif command.motion_type == MotionType.ABSOLUTE:
            min_distance = min_abs_distance
            max_distance = max_abs_distance
        else:
            min_distance = -math.inf
            max_distance = math.inf
        if not min_distance <= command.distance <= max_distance:
            raise BadRequestError("Wrong distance value, must be between "
                                  f"{min_distance} and {max_distance} mm")
    @abstractmethod
    def start(self, command: MotionCommand):
        """Starts a motion following the given motion command.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        # If there is already a motion running
        if self.is_busy():
            raise ConflictError("There is already a motion running. Stop it "
                                "before starting a new one.")
    @abstractmethod
    def stop(self):
        """Stops any running motion on this motor.
        Raises:
            InternalServerError: An error occurs in the process
        """
    def get_info(self) -> dict[str,]:
        """Returns information about this motor and its current motion.
        This returns a JSON object describing the different information.
        Raises:
            InternalServerError: An error occurs in the process
        """
        return {
            "running": self.is_busy(),
            "position": self.get_position(),
            "homed": self.is_homed(),
        }
    def get_command(self) -> dict[str,]:
        """Returns the current motion command or None if there is no current
        command.
        Returns:
            A JSON object representing the current command or None
        """
        if self.current_command:
            return self.current_command.to_json()
        return None
```

## File: motions/nanotec/__init__.py/__init__.py
```python
"""Packages that gathers every Motor implementation for a Nanotec driver.
The abstract classes Motor and MotionCommand are implemented here through a
generic NanotecDriver class and motor specific objects.
List of motor types currently available:
    - BLDC (Brushless DC motor)
"""
from alibrary.motions.nanotec.command import NanotecMotionCommand
from alibrary.motions.nanotec.motor import NanotecMotor, NanotecMotorConfig
__all__ = [
    "NanotecMotionCommand",
    "NanotecMotor",
    "NanotecMotorConfig",
]
```

## File: motions/nanotec/command.py/command.py
```python
"""Modules defining a Nanotec motion command.
This is an implementation of the abstract MotionCommand for a motor connected to
a Nanotec driver.
To handle the different kind of motors that we might use in the machines, an
abstract class is defined. This allows to have a template and to benefit from
OOP advantages for the motors and motions.
"""
from alibrary.motions.abstract.command import MotionCommand, MotionType
class NanotecMotionCommand(MotionCommand):
    """Implementation of the MotionCommand class for a motor connected to a
    Nanotec driver.
    """
    @classmethod
    def from_json(cls, json: dict[str,]) -> "NanotecMotionCommand":
        """Returns a NanotecMotionCommand from the given JSOn object.
        Args:
            json: A JSON object to deserialize
        Returns:
            A NanotecMotionCommand
        """
        motion_type = MotionType[str(
            json["mode"]).upper()] if "mode" in json else MotionType.RELATIVE
        speed = float(json["speed"]) if "speed" in json else 0.0
        distance = float(json["distance"]) if "distance" in json else 0.0
        turns = float(json["turns"]) if "turns" in json else 0.0
        return cls(motion_type=motion_type,
                   speed=speed,
                   distance=distance,
                   turns=turns)
    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this command.
        Returns:
            A JSON object representing this NanotecMotionCommand
        """
        json = {
            "mode": self.motion_type.name.lower(),
            "speed": self.speed,
            "distance": self.distance,
            "turns": self.turns
        }
        return json
```

## File: motions/nanotec/motor.py/motor.py
```python
"""Defines a class that controls a motor connected to a NanotecDriver.
This class implements the NanotecDriver class. It is a subclass of both
NanotecDriver and Motor. It has to define the Motor methods.
"""
from dataclasses import dataclass
from alibrary.electronics.nanotec import NanotecDriver, NanotecDriverError
from alibrary.motions.abstract.command import MotionType
from alibrary.motions.abstract.motor import Motor
from alibrary.motions.nanotec.command import NanotecMotionCommand
from alibrary.server import BadRequestError, ConflictError, InternalServerError
@dataclass
class NanotecMotorConfig:
    """Configuration variables of a Nanotec BLDC motor.
    Attributes:
        max_speed: The maximum speed allowed by the motor [mm/z]
        min_abs_distance: The minimum absolute position of the motor [mm]
        max_abs_distance: The maximum absolute position of the motor [mm]
        homing_speed: The speed of the homing procedure [mm/s]
        homing_acceleration: The acceleration of the homing procedure [mm/s²]
        search_zero_speed: The speed of the search zero procedure [mm/s]
        acceleration: The acceleration of the motions [mm/s²]
        deceleration: The decelerations of the motions [mm/s²]
        stop_deceleration: The deceleration when stopping the motions [mm/s²]
    """
    max_speed: float = 0.0
    min_abs_distance: float = 0.0
    max_abs_distance: float = 0.0
    homing_speed: float = 0.0
    homing_acceleration: float = 0.0
    search_zero_speed: float = 0.0
    acceleration: float = 0.0
    deceleration: float = 0.0
    stop_deceleration: float = 0.0
class NanotecMotor(Motor):
    """Implementation of the Motor class for a motor connected to a Nanotec
    driver.
    """
    def __init__(
        self,
        driver: NanotecDriver,
        config: NanotecMotorConfig,
        should_be_homed: bool = False,
        should_use_custom_homing: bool = False,
        should_halt: bool = False,
    ) -> None:
        self.config = config
        self.driver = driver
        self.__should_be_homed = should_be_homed
        self.__should_use_custom_homing = should_use_custom_homing
        self.__should_halt = should_halt
    def is_busy(self) -> bool:
        """Returns the running status of the motor.
        Returns:
            True if a motion is running on the motor, false otherwise
        Raises:
            InternalServerError: An error occurs in the process
        """
        try:
            return self.driver.is_busy()
        except NanotecDriverError as error:
            raise InternalServerError(str(error)) from error
    def get_position(self) -> float:
        """Gets the current position of the Nanotec driver.
        Returns:
            A float representing the position in mm
        Raises:
            InternalServerError: An error occurs in the process
        """
        try:
            position = self.driver.get_position()
            return position / 1000
        except NanotecDriverError as error:
            raise InternalServerError(str(error)) from error
    def is_homed(self) -> bool:
        """Returns the homing status of the motor.
        Returns:
            True if a homing has been performed on the motor, false otherwise
        Raises:
            InternalServerError: An error occurs in the process
        """
        try:
            return self.driver.is_homed()
        except NanotecDriverError as error:
            raise InternalServerError(str(error)) from error
    def validate_command(self, command: NanotecMotionCommand,
                         min_abs_distance: float, max_abs_distance: float):
        """Checks if the command is valid regarding to the motor current state
        and parameters.
        In addition of its parent class validation, it also checks if the given
        speed is valid
        Raises:
            BadRequestError: The given command is not valid
            InternalServerError: An error occurs in the process
        """
        if command.motion_type == MotionType.HOMING:
            return
        # Speed must be in ]0; max_speed]
        if command.speed <= 0 or command.speed > self.config.max_speed:
            raise BadRequestError("Wrong speed value, must be below "
                                  f"{self.config.max_speed} mm/s")
        super().validate_command(command, min_abs_distance, max_abs_distance)
    def start(self, command: NanotecMotionCommand):
        """Starts a motion following the given motion command.
        It will first call the parent method to check if there is no motion
        currently running. Then it checks if the command is valid before
        starting the motion.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        super().start(command)
        # Validate motion command
        self.validate_command(command, self.config.min_abs_distance,
                              self.config.max_abs_distance)
        if self.driver.offline:
            return
        # Check homing
        if (self.__should_be_homed and not self.driver.is_homed() and
                command.motion_type != MotionType.HOMING):
            raise ConflictError("Homing not done")
        # Select right method
        if command.motion_type == MotionType.HOMING:
            if self.__should_use_custom_homing:
                self.driver.perform_custom_homing(
                    int(self.config.homing_speed * 1000),
                    int(self.config.homing_acceleration * 1000),
                )
            else:
                self.driver.perform_homing(
                    int(self.config.homing_speed * 1000),
                    int(self.config.homing_acceleration * 1000),
                    int(self.config.search_zero_speed * 1000),
                )
        elif command.motion_type == MotionType.SPEED:
            self.driver.perform_speed_motion(
                int(command.speed * 1000),
                int(self.config.acceleration * 1000),
            )
        else:
            is_relative = command.motion_type == MotionType.RELATIVE
            self.driver.perform_distance_motion(
                int(command.distance * 1000),
                int(command.speed * 1000),
                int(self.config.acceleration * 1000),
                int(self.config.deceleration * 1000),
                is_relative,
            )
        self.current_command = command
    def stop(self):
        if self.driver.offline:
            return
        if self.__should_halt:
            self.driver.halt()
        else:
            self.driver.stop(int(self.config.stop_deceleration * 1000))
```

## File: motions/pcb/__init__.py/__init__.py
```python
"""Packages that gathers every Motor implementation for a PssPCB."""
from alibrary.motions.pcb.command import PCBScrewMotionCommand
from alibrary.motions.pcb.motor import PCBScrewConfig, PCBScrewMotor
__all__ = [
    "PCBScrewMotionCommand",
    "PCBScrewMotor",
    "PCBScrewConfig",
]
```

## File: motions/pcb/command.py/command.py
```python
"""Modules defining a differential screw motion command for a PssPCB stepper.
This is an implementation of the abstract MotionCommand for a PssPCB stepper.
To handle the different kind of motors that we might use in the machines, an
abstract class is defined. This allows to have a template and to benefit from
OOP advantages for the motors and motions.
"""
from alibrary.motions.abstract.command import MotionCommand, MotionType
class PCBScrewMotionCommand(MotionCommand):
    """Implementation of the MotionCommand class for a differential screw
    connected to the PssPCB.
    Attributes:
        motion_type: The type of motion
        distance: A float representing the distance traveled in the motion
    """
    @classmethod
    def from_json(cls, json: dict[str,]) -> "PCBScrewMotionCommand":
        """Returns a PCBScrewMotionCommand from the given JSOn object.
        Args:
            json: A JSON object to deserialize
        Returns:
            A PCBScrewMotionCommand
        """
        motion_type = MotionType[str(
            json["mode"]).upper()] if "mode" in json else MotionType.RELATIVE
        distance = float(json["distance"]) if "distance" in json else 0.0
        return cls(motion_type=motion_type, distance=distance)
    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this command.
        Returns:
            A JSON object representing this PCBScrewMotionCommand
        """
        json = {
            "mode": self.motion_type.name.lower(),
            "distance": self.distance,
        }
        return json
```

## File: motions/pcb/motor.py/motor.py
```python
"""Modules defining a differential screw stepper motor class.
This is an implementation of the abstract Motor for a PssPCB stepper.
To handle the different kind of motors that we might use in the machines, an
abstract class is used. This allows to have a template and to benefit from OOP
advantages for the motors and motions.
"""
import os
import pickle
from dataclasses import dataclass
from alibrary.electronics.pcb import PssPCB
from alibrary.motions.abstract.motor import Motor
from alibrary.motions.pcb.command import MotionType, PCBScrewMotionCommand
from alibrary.server import ConflictError
@dataclass
class PCBScrewConfig:
    """Dataclass with the configuration parameters of a scraping blade screw
    stepper.
    Attributes:
        steps_per_rev: The number of steps per revolution of the stepper
        microsteps_per_step: The number of microsteps per step of the stepper
        micron_per_rev: The number of micron traveled by revolution
        min_abs_distance: The minimum absolute distance of the stepper range
        max_abs_distance: The maximum absolute distance of the stepper range
    """
    steps_per_rev: int
    microsteps_per_step: int
    micron_per_rev: int
    min_abs_distance: float = 0.0
    max_abs_distance: float = 0.0
class PCBScrewMotor(Motor):
    """Implementation of the Motor class for a stepper connected to the PssPCB.
    """
    def __init__(self, index: int, pcb: PssPCB, config: PCBScrewConfig) -> None:
        self.index = index
        self.config = config
        self.pcb = pcb
        self.position = 0
        self.file_name = f"./logs/screw_stepper_{index}_position"
        self.__load_position()
    def __save_position(self):
        """Saves the current position into a json object in a file.
        If the file doesn't exist, it will be created
        """
        if not os.path.exists("./logs"):
            os.makedirs("./logs")
        with open(self.file_name, "wb") as f:
            steppers_position = {}
            steppers_position[self.index] = self.position
            pickle.dump(steppers_position, f)
    def __load_position(self):
        """Loads the current position from a json object in a file.
        """
        if self.pcb.offline:
            self.position = 300
            return
        if os.path.exists(self.file_name):
            with open(self.file_name, "rb") as f:
                steppers_position = pickle.load(f)
                self.position = steppers_position[self.index]
                raw_position = int(self.position * self.config.steps_per_rev *
                                   self.config.microsteps_per_step /
                                   self.config.micron_per_rev)
                self.pcb.set_actual_position(self.index, raw_position)
    def is_busy(self) -> bool:
        """Checks if this screw is busy.
        Returns:
            A bool indicating if it is busy or not
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        return (self.pcb.check_busy() >> self.index) % 2 == 1
        # return (self.pcb.check_busy() >> self.index) % 2 != 1
    def get_position(self) -> float:
        """Returns the current position of the screw.
        Returns:
            A float indicating the current position
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        # FIXME: Do I keep this ? Should it be used by the server ?
        # raw_position = self.pcb.get_actual_position(self.index)
        # position = -int(
        #     raw_position / self.config.steps_per_rev /
        #     self.config.microsteps_per_step * self.config.micron_per_rev)
        # logger.warning("Stepper %s: actual target position %s", self.index,
        #                position)
        # self.pcb.check_driver_communication()
        return self.position
    def is_homed(self) -> bool:
        """Checks if this screw is busy.
        Returns:
            A bool indicating if it is busy or not
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        return (self.pcb.check_homing_done() >> self.index) % 2 == 1
    def __perform_homing(self):
        """Performs the homing procedure on this screw.
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        self.pcb.perform_homing(self.index)
        self.position = 0
        self.__save_position()
    def __perform_distance_motion(self, distance: float):
        """Performs an absolute distance motion on this screw.
        Args:
            distance: The absolute distance to traveled
        Raises:
            PssPCBError: An error occurs in th communication with the PCB
        """
        raw_distance = int(distance * self.config.steps_per_rev *
                           self.config.microsteps_per_step /
                           self.config.micron_per_rev)
        self.pcb.perform_distance_motion(self.index, raw_distance)
        self.position = distance
        self.__save_position()
    def start(self, command: PCBScrewMotionCommand):
        """Starts a motion following the given motion command.
        It will first call the parent method to check if there is no motion
        currently running. Then it checks if the homing of the screw was done
        and if the command is valid before starting the motion.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        super().start(command)
        if not self.is_homed() and command.motion_type != MotionType.HOMING:
            raise ConflictError("Perform the homing before moving the blade")
        self.validate_command(command, self.config.min_abs_distance,
                              self.config.max_abs_distance)
        self.current_command = command
        if command.motion_type == MotionType.HOMING:
            self.__perform_homing()
        elif command.motion_type == MotionType.ABSOLUTE:
            self.__perform_distance_motion(command.distance)
        else:  #if command.motion_type == PCBScrewMotionType.RELATIVE:
            crt_position = self.get_position()
            self.__perform_distance_motion(command.distance + crt_position)
    def stop(self):
        """Deletes the registered current command."""
        self.current_command = None
```

## File: motions/rexroth/__init__.py/__init__.py
```python
"""Packages that gathers every Motor implementation for a PssPCB."""
from alibrary.motions.rexroth.command import RexrothMotionCommand
from alibrary.motions.rexroth.motor import RexrothMotor
__all__ = [
    "RexrothMotionCommand",
    "RexrothMotor",
]
```

## File: motions/rexroth/command.py/command.py
```python
"""Modules defining a Rexroth motion command.
This is an implementation of the abstract MotionCommand for a Rexroth axis.
To handle the different kind of motors that we might use in the machines, an
abstract class is defined. This allows to have a template and to benefit from
OOP advantages for the motors and motions.
"""
from alibrary.motions.abstract.command import MotionCommand, MotionType
class RexrothMotionCommand(MotionCommand):
    """Implementation of the MotionCommand class for a Rexroth motor.
    Attributes:
        motion_type: The type of motion
        distance: A float representing the distance traveled in the motion
    """
    @classmethod
    def from_json(cls, json: dict[str,]) -> "RexrothMotionCommand":
        """Returns a RexrothMotionCommand from the given JSOn object.
        Args:
            json: A JSON object to deserialize
        Returns:
            A RexrothMotionCommand
        """
        motion_type = MotionType[str(
            json["mode"]).upper()] if "mode" in json else MotionType.RELATIVE
        distance = float(json["distance"]) if "distance" in json else 0.0
        speed = float(json["speed"]) if "speed" in json else 0.0
        return cls(motion_type=motion_type, distance=distance, speed=speed)
    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this command.
        Returns:
            A JSON object representing this RexrothMotionCommand
        """
        json = {
            "mode": self.motion_type.name.lower(),
            "distance": self.distance,
            "speed": self.speed,
        }
        return json
```

## File: motions/rexroth/motor.py/motor.py
```python
"""Modules defining a differential screw stepper motor class.
This is an implementation of the abstract Motor for a PssPCB stepper.
To handle the different kind of motors that we might use in the machines, an
abstract class is used. This allows to have a template and to benefit from OOP
advantages for the motors and motions.
"""
from alibrary.electronics.rexroth import RexrothDotNetDriver
from alibrary.motions.abstract.motor import Motor
from alibrary.motions.rexroth.command import MotionType, RexrothMotionCommand
class RexrothMotor(Motor):
    """Implementation of the Motor class for a stepper connected to the PssPCB.
    """
    def __init__(
        self,
        driver: RexrothDotNetDriver,
        min_abs_distance: float = 0.0,
        max_abs_distance: float = 0.0,
    ) -> None:
        self.driver = driver
        self.min_abs_distance = min_abs_distance
        self.max_abs_distance = max_abs_distance
    def is_busy(self) -> bool:
        """Checks if this motor is busy.
        Returns:
            A bool indicating if it is busy or not
        Raises:
            RexrothError: An error occurs with the Rexroth driver
        """
        return self.driver.check_busy()
    def is_homed(self) -> bool:
        """Checks if this motor is busy.
        Returns:
            A bool indicating if it is busy or not
        Raises:
            RexrothError: An error occurs with the Rexroth driver
        """
        # TODO: Implement in the driver
        return True
    def get_position(self) -> float:
        """Returns the current position of the motor.
        Returns:
            A float indicating the current position
        Raises:
            RexrothError: An error occurs with the Rexroth driver
        """
        return self.driver.get_position()
    def start(self, command: RexrothMotionCommand):
        """Starts a motion following the given motion command.
        It will first call the parent method to check if there is no motion
        currently running. Then it checks if the homing of the screw was done
        and if the command is valid before starting the motion.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        super().start(command)
        self.validate_command(command, self.min_abs_distance,
                              self.max_abs_distance)
        self.current_command = command
        if command.motion_type == MotionType.ABSOLUTE:
            self.driver.perform_absolute_motion(command.distance, command.speed)
        elif command.motion_type == MotionType.RELATIVE:
            self.driver.perform_relative_motion(command.distance, command.speed)
    def stop(self):
        """Deletes the registered current command."""
        self.current_command = None
```

## File: pneumatic/__init__.py/__init__.py
```python
"""Package defining classes related to pneumatic elements of the machine."""
from alibrary.pneumatic.valve import PneumaticValve
from alibrary.pneumatic.hood_valve import HoodValve
from alibrary.pneumatic.pressure_regulator import PressureRegulator
from alibrary.pneumatic.tubes_ejection_regulator import TubesEjectionPressureRegulator
from alibrary.pneumatic.piezo_ejection_regulator import PiezoEjectionPressureRegulator
__all__ = [
    "PneumaticValve",
    "HoodValve",
    "PressureRegulator",
    "TubesEjectionPressureRegulator",
    "PiezoEjectionPressureRegulator",
]
```

## File: pneumatic/hood_valve.py/hood_valve.py
```python
"""Module defining the hood valve.
"""
import time
from alibrary.electronics.pcb import PssPCB, PssPCBError
from alibrary.logger import logger
from alibrary.pneumatic.valve import PneumaticValve
from alibrary.server import InternalServerError
class HoodValve(PneumaticValve):
    """A valve controlled by a custom PCB"""
    def __init__(
        self,
        sensor_index: int,
        stepper_index: int,
        p_range: tuple[int, int],
        pcb: PssPCB,
    ) -> None:
        super().__init__(control_index=None,
                         sensor_index=sensor_index,
                         stepper_index=stepper_index,
                         p_range=p_range,
                         pcb=pcb,
                         plc=None,
                         maximum=0)
        self.perform_homing()
        self.position = 0
    def set_initial_position(self, initial_position: int):
        """Waits for the valve to be homed before setting the initial position.
        Args:
            initial_position: The initial position of the valve
        """
        while not self.is_homing_done():
            time.sleep(0.1)
        logger.debug("Homing of the hood valve done")
        try:
            self.pcb.perform_distance_motion(self.stepper_index,
                                             initial_position)
            self.position = initial_position
        except PssPCBError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
    def set_position(self, json: dict[str,]):
        """
        Sets position in microsteps
        """
        try:
            position = json["position"] if "position" in json else 0
            self.pcb.perform_distance_motion(self.stepper_index, position)
            self.position = position
        except PssPCBError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
    def get_position(self) -> dict[str,]:
        """
        Returns the position
        """
        return {
            "position": self.position
        }
```

## File: pneumatic/piezo_ejection_regulator.py/piezo_ejection_regulator.py
```python
"""Module describing a pressure regulator for the ejection pressure of the
recoater.
"""
from alibrary.pneumatic.pressure_regulator import PressureRegulator
from alibrary.electronics.stm32 import STM32, STM32Error
from alibrary.server import InternalServerError
from alibrary.logger import logger
class PiezoEjectionPressureRegulator(PressureRegulator):
    """Implementation of PressureRegulator for the ejection pressure inside a
    a recoater with tubes.
    Attributes:
        stm32: A STM32 object
        drum_id: An integer representing the drum of which to modify the
        ejection
    """
    def __init__(self, stm32: STM32, drum_id: int, maximum: float) -> None:
        super().__init__(maximum)
        self.stm32: STM32 = stm32
        self.drum_id: int = drum_id
    @property
    def pressure(self) -> float:
        """Getter for the current pressure."""
        return self.stm32.get_ejection_pressure(self.drum_id)
    @PressureRegulator.target.setter
    def target(self, pressure: float) -> None:
        """Setter for the pressure target."""
        try:
            self.stm32.set_ejection_pressure(self.drum_id, pressure)
            self._target = pressure
        except STM32Error as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
```

## File: pneumatic/pressure_regulator.py/pressure_regulator.py
```python
"""Module describing a pressure regulator class."""
from abc import ABC, abstractmethod
class PressureRegulator(ABC):
    """A component that can regulate a pressure.
    """
    def __init__(self, maximum: float) -> None:
        self._pressure: float = 0
        self._target: float = 0
        self._maximum: float = maximum
    @property
    def maximum(self) -> float:
        """Getter for the maximum pressure"""
        return self._maximum
    @maximum.setter
    def maximum(self, maximum: float) -> None:
        """Setter for the maximum pressure"""
        self._maximum = maximum
    @property
    @abstractmethod
    def pressure(self) -> float:
        """Returns the current pressure.
        Returns:
            A float representing the current pressure.
        """
    @property
    def target(self) -> float:
        """Returns the current target pressure.
        Returns:
            A float representing the current target pressure.
        """
        return self._target
    @target.setter
    @abstractmethod
    def target(self, pressure: float) -> None:
        """Sets the target pressure."""
```

## File: pneumatic/tubes_ejection_regulator.py/tubes_ejection_regulator.py
```python
"""Module describing a pressure regulator for the ejection pressure of the
recoater.
"""
from alibrary.electronics.controllino.plc import ControllinoError
from alibrary.pneumatic.pressure_regulator import PressureRegulator
from alibrary.electronics.controllino import Controllino
from alibrary.server import InternalServerError
from alibrary.logger import logger
class TubesEjectionPressureRegulator(PressureRegulator):
    """Implementation of PressureRegulator for the ejection pressure inside a
    a recoater with tubes.
    Attributes:
        controllino: A Controllino object
        drum_id: An integer representing the drum of which to modify the
        ejection
    """
    def __init__(self, controllino: Controllino, drum_id: int,
                 maximum: float) -> None:
        super().__init__(maximum * 100000)
        self.controllino: Controllino = controllino
        self.drum_id: int = drum_id
    @property
    def pressure(self) -> float:
        """Getter for the current pressure"""
        max_in_bar = self.maximum / 100000
        raw_pressure = self.controllino.get_ejection(self.drum_id)
        pressure = (raw_pressure - 51) / 204 * max_in_bar
        return pressure * 100000
    @PressureRegulator.target.setter
    def target(self, pressure: float) -> None:
        """Setter for the target pressure"""
        try:
            max_in_bar = self.maximum / 100000
            pressure_in_bar = pressure / 100000
            raw_pressure = pressure_in_bar * 204 / max_in_bar + 51
            self.controllino.set_ejection(self.drum_id, raw_pressure)
            self._target = pressure
        except ControllinoError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
```

## File: pneumatic/valve.py/valve.py
```python
"""Module defining a pneumatic valve.
This kind of valve is used in the recoater to control the pressure in the drums
and in the leveler.
"""
from alibrary.electronics.controllino import (ControllinoError, Controllino)
from alibrary.electronics.pcb import PssPCB, PssPCBError
from alibrary.logger import logger
from alibrary.server import InternalServerError
from alibrary.pneumatic.pressure_regulator import PressureRegulator
class PneumaticValve(PressureRegulator):
    """A pneumatic valve controlled by a custom PCB.
    """
    N_MAX = 16384
    def __init__(self, control_index: int, sensor_index: int,
                 stepper_index: int, p_range: tuple[int, int], pcb: PssPCB,
                 plc: Controllino, maximum: float) -> None:
        super().__init__(maximum)
        self.control_index = control_index
        self.sensor_index = sensor_index
        self.stepper_index = stepper_index
        self.p_min = p_range[0]
        self.p_max = p_range[1]
        self.pcb = pcb
        self.plc = plc
    def __compute_pressure(self, data: int) -> float:
        """Converts a raw pressure measure in a correct pressure value.
        Args:
            data: The raw pressure measure
        Returns:
            A float representing the pressure value
        """
        return ((self.p_max - self.p_min) * (data - 0.1 * self.N_MAX) /
                (0.8 * self.N_MAX) + self.p_min)
    def __compute_data(self, pressure: float) -> int:
        """Converts a pressure value in a raw pressure setpoint.
        Args:
            data: The pressure value
        Returns:
            An int representing the raw pressure setpoint
        """
        return int((pressure - self.p_min) / (self.p_max - self.p_min) *
                   (0.8 * self.N_MAX) + 0.1 * self.N_MAX)
    @property
    def pressure(self) -> float:
        """Returns the current pressure measured at the valve.
        Returns:
            A float representing the measured pressure
        Raises:
            InternalServerError: An error occurs in th communication with the
            PCB
        """
        try:
            return self.__compute_pressure(
                self.pcb.get_raw_pressures()[self.sensor_index])
        except PssPCBError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
    @PressureRegulator.target.setter
    def target(self, pressure: float) -> None:
        """Sets the pressure that the valve should regulate.
        Args:
            pressure: The pressure that the valve must impose
        Raises:
            InternalServerError: An error occurs in th communication with the
            PCB
        """
        try:
            if pressure != 0:
                self.plc.activate_cyclone(self.control_index)
                self.activate_regulation(pressure)
            else:
                self.deactivate_regulation()
                self.plc.deactivate_cyclone(self.control_index)
                self.perform_homing()
            self._target = pressure
        except PssPCBError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
        except ControllinoError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
    def activate_regulation(self, pressure):
        """Activates the pressure regulation of this valve."""
        self.pcb.start_pressure_control(self.control_index,
                                        self.stepper_index,
                                        self.sensor_index,
                                        self.__compute_data(pressure))
    def deactivate_regulation(self):
        """Deactivates the pressure regulation of this valve."""
        self.pcb.stop_pressure_control(self.control_index)
    def perform_homing(self):
        """Performs the homing procedure on this valve.
        Raises:
            InternalServerError: An error occurs in th communication with the
            PCB
        """
        try:
            self.pcb.perform_homing(self.stepper_index)
        except PssPCBError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
    def is_homing_done(self) -> bool:
        """Checks if the homing of this valve has been done.
        Returns:
            A bool indicating if the homing is done or not
        Raises:
            InternalServerError: An error occurs in th communication with the
            PCB
        """
        try:
            return (self.pcb.check_homing_done() >> self.stepper_index) % 2 == 1
        except PssPCBError as error:
            logger.error(str(error))
            raise InternalServerError(str(error)) from error
```

## File: print/__init__.py/__init__.py
```python
"""Package gathering print related classes.
"""
from alibrary.print.parameters import PrintParameters
__all__ = [
    "PrintParameters",
]
```

## File: print/parameters.py/parameters.py
```python
"""Module describing the parameters of a print job."""
class PrintParameters:
    """List of the parameters specific to a print job.
    Attributes:
        filling_drum_id: The id of the drum with the filling material
        speed: The speed of the patterning
        x_offset: The pattern offset along the X axis
        powder_saving: A flag indicating if the powder saving techniques should
        be apply
        max_x_offset
    """
    def __init__(self,
                 filling_drum_id: int = -1,
                 patterning_speed: float = 0,
                 travel_speed: float = 0,
                 z_speed: float = 0,
                 x_offset: float = 0,
                 z_offset: float = 0,
                 layer_thickness: float = 0,
                 collectors_delay: int = 0,
                 layer_start: int = 0,
                 layer_end: int = 0,
                 powder_saving: bool = False) -> None:
        self.filling_drum_id = filling_drum_id
        self.patterning_speed = patterning_speed
        self.travel_speed = travel_speed
        self.z_speed = z_speed
        self.x_offset = x_offset
        self.z_offset = z_offset
        self.layer_thickness = layer_thickness
        self.powder_saving = powder_saving
        self.collectors_delay = collectors_delay
        self.layer_start = layer_start
        self.layer_end = layer_end
        # REVIEW: What value ? Where to initialize ? Is it really useful ?
        self.max_x_offset = 1000
    @classmethod
    def from_json(cls, json: dict[str,]) -> "PrintParameters":
        """Deserializes a JSON object.
        This method returns a PrintParameters object based on th given JSON.
        Args:
            json: The JSON object to deserialize
        Returns:
            A PrintParameters object
        """
        filling_id = int(json["filling_id"]) if "filling_id" in json else 0
        patterning_speed = float(
            json["patterning_speed"]) if "patterning_speed" in json else 0.0
        travel_speed = float(
            json["travel_speed"]) if "travel_speed" in json else 0.0
        z_speed = float(json["z_speed"]) if "z_speed" in json else 0.0
        x_offset = float(json["x_offset"]) if "x_offset" in json else 0.0
        z_offset = float(json["z_offset"]) if "z_offset" in json else 0.0
        layer_thickness = float(
            json["layer_thickness"]) if "layer_thickness" in json else 0.0
        collectors_delay = int(
            json["collectors_delay"]) if "collectors_delay" in json else 0
        layer_start = int(json["layer_start"]) if "layer_start" in json else 0
        layer_end = int(json["layer_end"]) if "layer_end" in json else 0
        powder_saving = bool(
            json["powder_saving"]) if "powder_saving" in json else False
        return cls(filling_drum_id=filling_id,
                   patterning_speed=patterning_speed,
                   travel_speed=travel_speed,
                   z_speed=z_speed,
                   x_offset=x_offset,
                   z_offset=z_offset,
                   layer_thickness=layer_thickness,
                   collectors_delay=collectors_delay,
                   layer_start=layer_start,
                   layer_end=layer_end,
                   powder_saving=powder_saving)
    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this PrintParameters.
        Returns:
            A JSON dictionary
        """
        json = {
            "filling_id": self.filling_drum_id,
            "patterning_speed": self.patterning_speed,
            "travel_speed": self.travel_speed,
            "z_speed": self.z_speed,
            "x_offset": self.x_offset,
            "z_offset": self.z_offset,
            "max_x_offset": self.max_x_offset,
            "layer_thickness": self.layer_thickness,
            "collectors_delay": self.collectors_delay,
            "layer_start": self.layer_start,
            "layer_end": self.layer_end,
            "powder_saving": self.powder_saving,
        }
        return json
```

## File: printer.py/printer.py
```python
"""Module defining a printer class responsible for handling multiple layers."""
import pycli
from alibrary.server import BadRequestError
class Printer:
    """Printer class responsible for handling multiple layers."""
    def __init__(self) -> None:
        self.clis: dict[int, pycli.CLI] = {}
        self.pngs: dict[int, bytes] = {}
        self.should_use_png: bool = False
        self.last_printed_layer_index: int = 0
        self.start_layer_index: int = 0
        self.end_layer_index: int = 0
        self.collectors_delay: int = 0
    def get_info(self) -> dict[str,]:
        n_layers = 0
        if self.should_use_png:
            n_layers = 1
        else:
            for cli in self.clis.values():
                if len(cli.geometry.layers) > n_layers:
                    n_layers = len(cli.geometry.layers)
        return {
            "n_layers": n_layers,
            "last_layer": self.last_printed_layer_index,
        }
    def set_drum_cli(self, drum_id: int, cli_file: bytes):
        try:
            self.clis[drum_id] = pycli.parse(cli_file)
            self.should_use_png = False
            self.last_printed_layer_index = 0
        except pycli.ParsingError as error:
            raise BadRequestError(
                f"Error with CLI file: {str(error)}") from error
    def set_drum_png(self, drum_id: int, png_file: bytes):
        self.last_printed_layer_index = 0
        self.pngs[drum_id] = png_file
        self.should_use_png = True
    def get_layer_for_drum(self, layer_id: int, drum_id: int):
        if self.should_use_png and drum_id in self.pngs:
            return self.pngs[drum_id]
        if not self.should_use_png and drum_id in self.clis:
            return self.clis[drum_id].sub_cli(layer_id, layer_id +
                                              1).to_ascii().encode("ascii")
        return None
    def remove_geometry(self, drum_id: int):
        self.clis.pop(drum_id, None)
        self.pngs.pop(drum_id, None)
```

## File: recoater/__init__.py/__init__.py
```python
"""Package gathering recoater specific classes.
Those classes are software equivalent of the different hardware component of
the recoater. They are independent from each other and are only gathered in the
folder structure. Only the direct children are exposed by this package, the
subfolders have to be imported separately and expose their own children.
"""
from alibrary.recoater.bridge_breakers import BridgeBreakers
from alibrary.recoater.config import BuildPlate, RecoaterConfig
from alibrary.recoater.leveler import Leveler, LevelerWithBlade
from alibrary.recoater.shovels import Shovels
__all__ = [
    "BridgeBreakers",
    "RecoaterConfig",
    "BuildPlate",
    "Leveler",
    "LevelerWithBlade",
    "Shovels",
]
```

## File: recoater/bridge_breakers.py/bridge_breakers.py
```python
"""Module defining the bridge breakers of a recoater"""
from alibrary.electronics import Controllino
from alibrary.logger import logger
class BridgeBreakers:
    """Interface to control the bridge breakers of the recoater"""
    def __init__(self, plc: Controllino) -> None:
        self.plc = plc
    def get_state(self) -> dict[str,]:
        """Returns the state of the bridge breakers
        Returns:
            A JSON object describing the state of the bridge breakers
        Raises:
            InternalServerError: An error occurs in the process
        """
        state = self.plc.get_bridge_breakers_state()
        return {"state": state}
    def set_state(self, json: dict[str,]) -> None:
        """Sets the state of the bridge breakers
        Args:
            A JSON object describing the state of the bridge breakers
        Raises:
            InternalServerError: An error occurs in the process
        """
        if "state" in json:
            new_state = json["state"]
            self.plc.set_bridge_breakers_state(new_state)
            logger.debug("Bridge breakers set to %s", new_state)
```

## File: recoater/config.py/config.py
```python
"""Module describing a recoater configuration class"""
from dataclasses import dataclass
@dataclass
class BuildPlate:
    """Dimensions of the build plate
    Attributes:
        diameter: The diameter of a circular build plate
        length: The length of a rectangular build plate
        width: The width of a rectangular build plate
    """
    diameter: float | None = None
    length: float | None = None
    width: float | None = None
    def has_length_and_width(self) -> bool:
        """Checks if this build plate has a length and a width defined
        Returns:
            True if both length and width are defined, false otherwise
        """
        return self.length is not None and self.width is not None
    def has_diameter(self) -> bool:
        """Checks if this build plate has a diameter defined
        Returns:
            True if diameter is defined, false otherwise
        """
        return self.diameter is not None
class RecoaterConfig:
    """A recoater configuration
    Attributes:
        resolution: The size of a pixel in this recoater deposition [µm]
        gaps: A list of float representing the gaps between the drums
        build_plate: A BuildPlate definition
    """
    def __init__(
            self,
            resolution: int,
            ejection_matrix_size: int,
            gaps: list[float],
            build_plate: BuildPlate = BuildPlate(),
    ) -> None:
        self.resolution = resolution
        self.ejection_matrix_size = ejection_matrix_size
        self.gaps = gaps
        self.build_plate = build_plate
    @classmethod
    def from_json(cls, json: dict[str,]) -> "RecoaterConfig":
        """Deserializes a JSON object.
        This method returns a RecoaterConfig object based on th given JSON.
        Args:
            json: The JSON object to deserialize
        Returns:
            A RecoaterConfig object
        """
        resolution = int(json["resolution"]) if "resolution" in json else 0
        ejection_matrix_size = int(json["ejection_matrix_size"]
                                  ) if "ejection_matrix_size" in json else 0
        gaps = json["gaps"] if "gaps" in json else [0.0]
        diameter = float(json["build_space_diameter"]
                        ) if "build_space_diameter" in json else None
        length = None
        width = None
        if "build_space_dimensions" in json:
            dimensions = json["build_space_dimensions"]
            length = float(
                dimensions["length"]) if "length" in dimensions else None
            width = float(
                dimensions["width"]) if "width" in dimensions else None
        build_plate = BuildPlate(diameter=diameter, length=length, width=width)
        return cls(resolution=resolution,
                   ejection_matrix_size=ejection_matrix_size,
                   gaps=gaps,
                   build_plate=build_plate)
    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this RecoaterConfig.
        Returns:
            A JSON dictionary
        """
        json = {
            "resolution": self.resolution,
            "ejection_matrix_size": self.ejection_matrix_size,
            "gaps": self.gaps,
        }
        if self.build_plate.has_length_and_width():
            json["build_space_dimensions"] = {
                "length": self.build_plate.length,
                "width": self.build_plate.width
            }
        if self.build_plate.has_diameter():
            json["build_space_diameter"] = self.build_plate.diameter
        return json
```

## File: recoater/drums/__init__.py/__init__.py
```python
"""Module describing drum related classes"""
from alibrary.recoater.drums.blade import Blade, Screw
from alibrary.recoater.drums.drum import Drum
from alibrary.recoater.drums.config import DrumConfig
from alibrary.recoater.drums.decorators import BladeDecorator, CollectorDecorator
from alibrary.recoater.drums.drums import Drums
__all__ = [
    "Drum",
    "DrumConfig",
    "Drums",
    "Blade",
    "Screw",
    "BladeDecorator",
    "CollectorDecorator",
]
```

## File: recoater/drums/blade.py/blade.py
```python
"""Module describing a scraping blade screw"""
from dataclasses import dataclass
from alibrary.motions.pcb.command import PCBScrewMotionCommand
from alibrary.motions.pcb.motor import PCBScrewMotor
@dataclass
class Screw:
    """Differential screw of a scraping blade."""
    index: int
    motor: PCBScrewMotor
    def get_info(self):
        """Returns information about this screw.
        It gets the information of the motor and append screw specific info.
        Returns:
            A JSON object describing this screw
        Raises:
            InternalServerError: An error occurs in the process
        """
        # Get motor info
        info = self.motor.get_info()
        # Add screw info
        info["id"] = self.index
        return info
    def get_command(self) -> dict[str,]:
        """Returns the current motion command or None if there is no current
        command.
        Returns:
            A JSON object representing the current command or None
        """
        return self.motor.get_command()
    def start_motion(self, command: PCBScrewMotionCommand):
        """Starts a motion following the given motion command.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        self.motor.start(command)
    def stop_motion(self):
        """Stops any running motion on this motor.
        Raises:
            InternalServerError: An error occurs in the process
        """
        self.motor.stop()
class Blade(list[Screw]):
    """A scaping blade with two differential screws"""
    def get_info(self) -> list[dict[str,]]:
        """Returns the list of the drums info.
        Returns:
            A list of the drums'info
        Raises:
            InternalServerError: An error occurs in the process
        """
        return [screw.get_info() for screw in self]
    def start_motion(self, command: PCBScrewMotionCommand):
        """Starts a blade motion.
        This will executes the given command on this blade's both screws.
        Args:
            command: A PssPCBMotionCommand representing the motion to execute
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            MotorBusyError: The motor is busy with another motion
        """
        for screw in self:
            screw.motor.start(command)
    def stop_motion(self):
        """Stops a blade motion.
        This will stops any motion on this blade's both screws.
        Raises:
            InternalServerError: An error occurs in the process
        """
        for screw in self:
            screw.motor.stop()
    def get_motion_info(self):
        """Returns the current motion command of this blade.
        Raises:
            InternalServerError: An error occurs in the process
        """
        return self[0].motor.get_command()
    def is_above_threshold(self) -> bool:
        """Checks if both screws of this blade are above a given threshold
        """
        threshold = 100
        for screw in self:
            if screw.motor.get_position() < threshold:
                return False
        return True
```

## File: recoater/drums/config.py/config.py
```python
"""Module defining a configuration object for an Aerosint drum."""
from dataclasses import dataclass
@dataclass
class DrumConfig:
    """Dataclass with all the configuration variables of a drum.
    Attributes:
        circumference: The circumference of the drum [mm]
        # max_suction_pressure: The maximum pressure for this drum suction [bar]
        # max_ejection_pressure: The maximum pressure for this drum ejection [bar]
        pixel_size: The size of a pixel of this drum [mm]
        geometry_size: The size of this drum build space
        enhancement_factor: A dilatation factor that improves the CLI drawings
    """
    circumference: float = 0.0
    # max_suction_pressure: float = 0
    # max_ejection_pressure: float = 0
    pixel_size: int = 0
    geometry_size: tuple[int, int] = (192, 192)
    enhancement_factor: int = 10
```

## File: recoater/drums/decorators/__init__.py/__init__.py
```python
"""Module defining decorator classes of a drum"""
from alibrary.recoater.drums.decorators.decorator import DrumDecorator
from alibrary.recoater.drums.decorators.blade import BladeDecorator
from alibrary.recoater.drums.decorators.collector import CollectorDecorator
__all__ = ["DrumDecorator", "BladeDecorator", "CollectorDecorator"]
```

## File: recoater/drums/decorators/blade.py/blade.py
```python
"""Module defining a subclass of Drum that integrates a motorized scraping
blade.
"""
from alibrary.motions.abstract.command import MotionCommand
from alibrary.recoater.drums.blade import Blade
from alibrary.recoater.drums.interface import DrumInterface
from alibrary.recoater.drums.decorators.decorator import DrumDecorator
from alibrary.server import ConflictError
class BladeDecorator(DrumDecorator):
    """Decorator class for a drum that adds the scraping blade features."""
    def __init__(self, drum: DrumInterface, blade: Blade) -> None:
        super().__init__(drum)
        self._blade: Blade = blade
    @property
    def blade(self) -> Blade:
        """The blade added to this decorated drum."""
        return self._blade
    def start_motion(self, command: MotionCommand):
        """Starts a motion following the given motion command.
        This override first checks if the blade is above a given threshold
        before starting the motion.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        if not self.blade.is_above_threshold():
            raise ConflictError("Scraping blade to low to start a drum motion")
        return super().start_motion(command)
```

## File: recoater/drums/decorators/collector.py/collector.py
```python
"""Module defining a subclass of Drum that integrates a powder collector.
"""
from alibrary.recoater.drums.decorators.decorator import DrumDecorator
from alibrary.recoater.drums.interface import DrumInterface
from alibrary.electronics.controllino.controllino import Controllino
from alibrary import logger
class CollectorDecorator(DrumDecorator):
    """Drum equipped with a powder collector.
    """
    def __init__(self, drum: DrumInterface, controllino: Controllino) -> None:
        super().__init__(drum)
        self._controllino = controllino
    @property
    def collector(self) -> dict[str,]:
        """Returns the current state of the powder collector.
        Returns:
            A JSON object describing the collector's state
        """
        return {"state": self._controllino.get_collectors(self._drum.index)}
    @collector.setter
    def collector(self, state: dict[str,]) -> None:
        """Sets the state of the powder collector.
        Args:
            A JSON object describing the collector state
        """
        state = state["state"]
        self._controllino.set_collectors(self._drum.index, state)
        logger.info("Drum %d powder collector set to %s", self._drum.index,
                    state)
```

## File: recoater/drums/decorators/decorator.py/decorator.py
```python
"""Module defining a drum decorator."""
import numpy as np
from alibrary.recoater.drums.interface import DrumInterface
from alibrary.motions.abstract.command import MotionCommand
from alibrary.motions.abstract.motor import Motor
class DrumDecorator(DrumInterface):
    """The base decorator class for a drum."""
    _drum: DrumInterface = None
    def __init__(self, drum: DrumInterface) -> None:
        self._drum = drum
    @property
    def drum(self) -> DrumInterface:
        """The drum decorated by this class."""
        return self._drum
    @property
    def geometry(self) -> np.ndarray:
        """Returns the geometry of this drum."""
        return self._drum.geometry
    @geometry.setter
    def geometry(self, geometry: np.ndarray) -> None:
        """Sets the geometry of this drum."""
        self._drum.geometry = geometry
    @property
    def theta_offset(self) -> float:
        """Returns the theta offset of this drum."""
        return self._drum.theta_offset
    @theta_offset.setter
    def theta_offset(self, offset: float) -> None:
        """Sets the theta offset of this drum."""
        self._drum.theta_offset = offset
    @property
    def powder_offset(self) -> float:
        """Returns the powder offset of this drum."""
        return self._drum.powder_offset
    @powder_offset.setter
    def powder_offset(self, offset: float) -> None:
        """Sets the powder offset of this drum."""
        self._drum.powder_offset = offset
    @property
    def motor(self) -> Motor:
        """Returns a JSON representation of this drum config.
        Returns:
            A JSON object describing the drum config
        """
        return self._drum.motor
    def get_info(self) -> dict[str,]:
        """Returns information about this drum.
        It gets the information of the motor and append drum specific info.
        Returns:
            A JSON object describing this drum
        Raises:
            InternalServerError: An error occurs in the process
        """
        return self._drum.get_info()
    @property
    def config(self) -> dict[str,]:
        """Returns a JSON representation of this drum config.
        Returns:
            A JSON object describing the drum config
        """
        return self._drum.config
    @config.setter
    def config(self, config: dict[str,]) -> None:
        """Sets configuration variables of this drum.
        Args:
            A JSON abject with the configuration variables
        """
        self._drum.config = config
    def get_ejection(self, unit: str) -> dict[str,]:
        """Returns the current, target and max values of this drum ejection.
        Returns:
            A JSON object describing the different ejection values
        Raises:
            InternalServerError: An error occurs in the process
        """
        return self._drum.get_ejection(unit)
    def set_ejection(self, pressure: dict[str,]) -> None:
        """Sets the requested ejection pressure.
        Args:
            A JSON object describing the target ejection pressure
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The request pressure is invalid
        """
        self._drum.set_ejection(pressure)
    @property
    def suction(self) -> dict[str,]:
        """Returns the current, target and max values of this drum suction.
        Returns:
            A JSON object describing the different suction values
        Raises:
            InternalServerError: An error occurs in the process
        """
        return self._drum.suction
    @suction.setter
    def suction(self, pressure: dict[str,]) -> None:
        """Sets the requested suction pressure.
        Args:
            A JSON object describing the target suction pressure
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The request pressure is invalid
        """
        self._drum.suction = pressure
    def get_motion_command(self) -> dict[str,]:
        """Returns the current motion command or None if there is no current
        command.
        Returns:
            A JSON object representing the current command or None
        """
        return self._drum.get_motion_command()
    def start_motion(self, command: MotionCommand):
        """Starts a motion following the given motion command.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        self._drum.start_motion(command)
    def stop_motion(self):
        """Stops any running motion on this motor.
        Raises:
            InternalServerError: An error occurs in the process
        """
        self._drum.stop_motion()
    def get_geometry(self) -> bytes:
        """Returns a PNG image with the current geometry of this drum.
        Returns:
            A bytes object representing the PNG image;
        """
        return self._drum.get_geometry()
    def remove_geometry(self) -> None:
        """Removes the current geometry"""
        self._drum.remove_geometry()
    def set_geometry_png(self, png: bytes) -> None:
        """Defines the geometry of this drum based on the given PNG image.
        Args:
            png: A bytes object representing the PNG image
        """
        self._drum.set_geometry_png(png)
    def set_geometry_cli(self, cli_file: bytes) -> None:
        """Defines the geometry of this drum based on the given CLI file.
        Args:
            cli_file: A bytes object representing the CLI file
        """
        self._drum.set_geometry_cli(cli_file)
```

## File: recoater/drums/drum.py/drum.py
```python
"""Module describing a drum of an Aerosint recoater."""
import cv2
import numpy as np
import pycli
from pycli.models import CLI
from alibrary.logger import logger
from alibrary.motions.abstract.command import MotionCommand, MotionType
from alibrary.motions.abstract.motor import Motor
from alibrary.pneumatic.valve import PneumaticValve
from alibrary.server import BadRequestError
from alibrary.recoater.drums.config import DrumConfig
from alibrary.recoater.drums.interface import DrumInterface
from alibrary.pneumatic.pressure_regulator import PressureRegulator
class Drum(DrumInterface):
    """Drum of an Aerosint recoater.
    Attributes:
        index: This drum index
        valve: The pneumatic valve responsible for the suction of the drum
        motor: The motor responsible for the theta motion of this drum
        controllino: The PLC responsible for the ejection pressure of this drum
        config: The configuration of this drum
        geometry: Tis drum powder geometry, stored as a deposition matrix
        theta_offset: The offset to apply along the theta axis of this drum
        powder_offset: The powder offset to apply on this drum geometry
        target_suction_pressure: The target suction pressure of this drum
    """
    def __init__(
            self,
            index: int,
            valve: PneumaticValve,
            motor: Motor,
            ejection: PressureRegulator,
            config: DrumConfig = DrumConfig(),
    ) -> None:
        super().__init__()
        self._index: int = index
        self._valve: PneumaticValve = valve
        self._motor: Motor = motor
        self._ejection: PressureRegulator = ejection
        self._config: DrumConfig = config
        self.geometry: np.ndarray = np.zeros(config.geometry_size,
                                             dtype=np.uint8)
        self.theta_offset: float = 0.0
        self.powder_offset: int = 0
        self._target_suction_pressure = 0.0
    @property
    def index(self) -> int:
        """Returns this drum motor"""
        return self._index
    @property
    def motor(self) -> Motor:
        """Returns this drum motor"""
        return self._motor
    def get_info(self) -> dict[str,]:
        """Returns information about this drum.
        It gets the information of the motor and append drum specific info.
        Returns:
            A JSON object describing this drum
        Raises:
            InternalServerError: An error occurs in the process
        """
        # Get motor info
        info = self._motor.get_info()
        # Add drum info
        info["circumference"] = self._config.circumference
        info["id"] = self.index
        return info
    @property
    def config(self) -> dict[str,]:
        """Returns a JSON representation of this drum config.
        Returns:
            A JSON object describing the drum config
        """
        config = {
            "theta_offset": self.theta_offset,
            "powder_offset": self.powder_offset
        }
        return config
    @config.setter
    def config(self, config: dict[str,]) -> None:
        """Sets configuration variables of this drum.
        Args:
            A JSON abject with the configuration variables
        """
        # Theta offset
        if "theta_offset" in config:
            self.theta_offset = config["theta_offset"]
        # Powder offset
        if "powder_offset" in config:
            self.powder_offset = config["powder_offset"]
    def get_ejection(self, unit: str) -> dict[str,]:
        """Returns the current, target and max values of this drum ejection.
        Returns:
            A JSON object describing the different ejection values
        Raises:
            InternalServerError: An error occurs in the process
        """
        if unit == "bar":
            value = self._ejection.pressure / 100000
            target = self._ejection.target / 100000
            maximum = self._ejection.maximum / 100000
        else:
            value = self._ejection.pressure
            target = self._ejection.target
            maximum = self._ejection.maximum
        json = {
            "maximum": maximum,
            "value": value,
            "target": target,
            "unit": unit,
        }
        return json
    def set_ejection(self, pressure: dict[str,]) -> None:
        """Sets the requested ejection pressure.
        Args:
            A JSON object describing the target ejection pressure
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The request pressure is invalid
        """
        target: float = pressure["target"]
        unit = pressure["unit"]
        if unit == "bar":
            target_in_pascal = target * 100000
        else:
            target_in_pascal = target
        maximum = self._ejection.maximum
        # Validation
        if not 0 <= target_in_pascal <= maximum:
            raise BadRequestError(
                f"(drum) The ejection pressure of the drum {self.index + 1}"
                f" should be between 0 and {maximum} but received"
                f" {target_in_pascal}")
        self._ejection.target = target_in_pascal
        logger.info("Drum %d ejection pressure set to %f %s", self.index,
                    target, unit)
    @property
    def suction(self) -> dict[str,]:
        """Returns the current, target and max values of this drum suction.
        Returns:
            A JSON object describing the different suction values
        Raises:
            InternalServerError: An error occurs in the process
        """
        values = {
            "target": self._valve.target,
            "maximum": self._valve.maximum,
            "value": self._valve.pressure
        }
        return values
    @suction.setter
    def suction(self, pressure: dict[str,]) -> None:
        """Sets the requested suction pressure.
        Args:
            A JSON object describing the target suction pressure
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The request pressure is invalid
        """
        pressure = pressure["target"]
        # Validation
        if not 0 <= pressure <= self._valve.maximum:
            raise BadRequestError("(drum) The suction target pressure of the "
                                  f"drum {self.index + 1} should be between 0 "
                                  f"and {self._valve.maximum} "
                                  f"but received {pressure}")
        # Application
        self._valve.target = pressure
        logger.info("Drum %d suction target pressure set to %f", self.index,
                    pressure)
    def get_motion_command(self) -> dict[str,]:
        """Returns the current motion command or None if there is no current
        command.
        Returns:
            A JSON object representing the current command or None
        """
        return self._motor.get_command()
    def start_motion(self, command: MotionCommand):
        """Starts a motion following the given motion command.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
        if command.motion_type == MotionType.TURNS:
            command.motion_type = MotionType.RELATIVE
            command.distance = command.turns * self._config.circumference
        if command.motion_type == MotionType.ABSOLUTE:
            crt_pos = self._motor.get_position()
            if command.distance < crt_pos:
                command.distance += self._config.circumference
        self._motor.start(command)
    def stop_motion(self):
        """Stops any running motion on this motor.
        Raises:
            InternalServerError: An error occurs in the process
        """
        self._motor.stop()
    def get_geometry(self) -> bytes:
        """Returns a PNG image with the current geometry of this drum.
        Returns:
            A bytes object representing the PNG image;
        """
        # Build BGR PNG image from the 2D binary matrix of this drum's geometry
        kernel = np.ones((3, 3), np.uint8)
        geo = cv2.dilate(self.geometry, kernel, iterations=self.powder_offset)
        image = 1 - np.tile(geo, (3, 1, 1))
        image *= 255
        image = np.moveaxis(image, 0, -1)
        # Converts numpy array into PNG bytes string
        png = cv2.imencode(".png", image)[1].tobytes()
        return png
    def remove_geometry(self) -> None:
        """Removes the current geometry"""
        self.geometry: np.ndarray = np.zeros(self._config.geometry_size,
                                             dtype=np.uint8)
    def set_geometry_png(self, png: bytes) -> None:
        """Defines the geometry of this drum based on the given PNG image.
        Args:
            png: A bytes object representing the PNG image
        """
        # Decode PNG into numpy array
        image: np.ndarray = cv2.imdecode(np.frombuffer(png, np.uint8), -1)
        # Convert partial transparency to either opaque or fully transparent
        if image.shape[2] == 4:
            image[:, :, 3] = np.floor(image[:, :, 3] / 255 + 0.5) * 255
        # List unique colors
        unique_colors = np.unique(image.reshape(-1, image.shape[2]), axis=0)
        # Remove transparent colors
        if unique_colors.shape[1] == 4:
            unique_colors = unique_colors[np.where(unique_colors[:, 3] != 0)]
        # Remove pure white
        unique_colors = unique_colors[np.any(unique_colors != 255, axis=1)]
        # Check the number of colors detected in the image
        if unique_colors.shape[0] > 1:
            raise BadRequestError("The image should be monochromatic.")
        color = unique_colors[0]
        # Select all pixels of the detected color
        layer: np.ndarray = np.all(image == color, axis=-1) * 1
        # self.geometry = self.__resize_canvas(layer)
        self.geometry = self.__resize_layer(layer).astype(np.uint8)
    def set_geometry_cli(self, cli_file: bytes) -> None:
        """Defines the geometry of this drum based on the given CLI file.
        Args:
            cli_file: A bytes object representing the CLI file
        """
        if cli_file == b"":
            self.geometry = np.zeros(self.geometry.shape, dtype=np.uint8)
        else:
            try:
                cli = pycli.parse(cli_file)
            except pycli.ParsingError as error:
                raise BadRequestError(
                    f"Error with CLI file: {str(error)}") from error
            self.geometry = self.__resize_canvas(self.__draw_cli(cli))
    def __draw_cli(self, cli: CLI) -> np.ndarray:
        """Draws the CLI onto the given canvas.
        This function will only draw the first non empty layer of the CLI. An
        empty layer is a layer with no polylines. This function modify the
        coordinates'axes. The origin is translated from the bottom left corner
        to the center of the build space. The Y axis is also flipped to better
        suit the way data is represented inside a Numpy array.
        Args:
            cli: The CLI object to draw
        """
        canvas = np.zeros(
            tuple(e * self._config.enhancement_factor
                  for e in self.geometry.shape))
        pixel_size = self._config.pixel_size / self._config.enhancement_factor
        # Loop through the polylines
        for polyline in cli.geometry.layers[0].polylines:
            if polyline.orientation == 1:
                fill = 1
            else:
                fill = -1
            poly_canvas = np.zeros(canvas.shape, dtype=np.int32)
            points = np.array(polyline, float)
            # Center coordinates
            points *= cli.header.units / pixel_size
            points[:, 0] = +points[:, 0] + canvas.shape[1] / 2
            points[:, 1] = -points[:, 1] + canvas.shape[0] / 2
            # Round to get int32 data
            points = points.round().astype(np.int32)
            # Draw filled polygon
            cv2.fillPoly(poly_canvas, [points], fill)
            canvas += poly_canvas
        canvas = np.clip(canvas, 0, 1)
        return canvas
    def __resize_canvas(self, canvas: np.ndarray) -> np.ndarray:
        """Resizes the given canvas
        Args:
            canvas: The canvas to resize
        Returns:
            A resized ndarray
        """
        return cv2.resize(canvas,
                          dsize=tuple(reversed(self.geometry.shape)),
                          interpolation=cv2.INTER_NEAREST).astype(np.uint8)
    def __resize_layer(self, layer: np.ndarray) -> np.ndarray:
        """Resizes the given layer to fit the build space dimensions set
        in the configuration. It will cut off the pattern if it is too large or
        it will pad it if it is too small.
        """
        # Loaded image siz
        img_height, img_width = layer.shape
        # Build space size
        bs_height = self.geometry.shape[0]
        bs_width = self.geometry.shape[1]
        # Scaling factors
        scale_height = bs_height / img_height
        scale_width = bs_width / img_width
        # Repeat factors
        if scale_height <= 1:
            pad_height = 0
        else:
            pad_height = bs_height - img_height
        if scale_width <= 1:
            pad_width = 1
        else:
            pad_width = bs_width - img_width
        repeated_layer = np.pad(layer, ((0, pad_height), (pad_width, 0)))
        resized_layer = repeated_layer[:bs_height, :bs_width]
        return resized_layer
```

## File: recoater/drums/drums.py/drums.py
```python
"""Module describing the set of drums of a recoater."""
import numpy as np
from alibrary.recoater.drums.interface import DrumInterface
from alibrary.server import NotFoundError
class Drums(list[DrumInterface]):
    """Set of drums on an Aerosint recoater."""
    def __getitem__(self, index):
        try:
            return super().__getitem__(index)
        except IndexError as error:
            raise NotFoundError(str(error)) from error
    def get_info(self) -> list[dict[str,]]:
        """Returns the list of the drums info.
        Returns:
            A list of the drums'info
        Raises:
            InternalServerError: An error occurs in the process
        """
        return [drum.get_info() for drum in self]
    def get_geometries(self) -> np.ndarray:
        """Returns all the geometries of its children
        Returns:
            An ndarray containing the geometry of each children drum
        """
        return np.array([drum.geometry for drum in self])
```

## File: recoater/drums/interface.py/interface.py
```python
"""Module defining a drum interface"""
from abc import ABC, abstractmethod
import numpy as np
from alibrary.electronics.controllino import Controllino
from alibrary.motions.abstract.command import MotionCommand
from alibrary.motions.abstract.motor import Motor
from alibrary.pneumatic.valve import PneumaticValve
from alibrary.recoater.drums.config import DrumConfig
class DrumInterface(ABC):
    """Abstract interface of a drum of an Aerosint recoater.
    """
    # def __init__(self) -> None:
    index: int = 0
    _valve: PneumaticValve = None
    _motor: Motor = None
    _controllino: Controllino = None
    _config: DrumConfig = DrumConfig()
    geometry: np.ndarray = None
    theta_offset: float = 0.0
    powder_offset: int = 0
    _target_suction_pressure = 0.0
    # @property
    # def index(self) -> int:
    #     """Returns this drum index"""
    @property
    def motor(self) -> Motor:
        """Returns this drum motor"""
        return self._motor
    # @property
    # def theta_offset(self) -> float:
    #     """Returns this drum theta offset"""
    # @property
    # def powder_offset(self) -> int:
    #     """Returns this drum theta offset"""
    @abstractmethod
    def get_info(self) -> dict[str,]:
        """Returns information about this drum.
        It gets the information of the motor and append drum specific info.
        Returns:
            A JSON object describing this drum
        Raises:
            InternalServerError: An error occurs in the process
        """
    @property
    @abstractmethod
    def config(self) -> dict[str,]:
        """Returns a JSON representation of this drum config.
        Returns:
            A JSON object describing the drum config
        """
    @config.setter
    @abstractmethod
    def config(self, config: dict[str,]) -> None:
        """Sets configuration variables of this drum.
        Args:
            A JSON abject with the configuration variables
        """
    @abstractmethod
    def get_ejection(self, unit: str) -> dict[str,]:
        """Returns the current, target and max values of this drum ejection.
        Returns:
            A JSON object describing the different ejection values
        Raises:
            InternalServerError: An error occurs in the process
        """
    @abstractmethod
    def set_ejection(self, pressure: dict[str,]) -> None:
        """Sets the requested ejection pressure.
        Args:
            A JSON object describing the target ejection pressure
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The request pressure is invalid
        """
    @property
    @abstractmethod
    def suction(self) -> dict[str,]:
        """Returns the current, target and max values of this drum suction.
        Returns:
            A JSON object describing the different suction values
        Raises:
            InternalServerError: An error occurs in the process
        """
    @suction.setter
    @abstractmethod
    def suction(self, pressure: dict[str,]) -> None:
        """Sets the requested suction pressure.
        Args:
            A JSON object describing the target suction pressure
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The request pressure is invalid
        """
    @abstractmethod
    def get_motion_command(self) -> dict[str,]:
        """Returns the current motion command or None if there is no current
        command.
        Returns:
            A JSON object representing the current command or None
        """
    @abstractmethod
    def start_motion(self, command: MotionCommand):
        """Starts a motion following the given motion command.
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The given command is not valid
            ConflictError: The motor is busy with another motion
        """
    @abstractmethod
    def stop_motion(self):
        """Stops any running motion on this motor.
        Raises:
            InternalServerError: An error occurs in the process
        """
    @abstractmethod
    def get_geometry(self) -> bytes:
        """Returns a PNG image with the current geometry of this drum.
        Returns:
            A bytes object representing the PNG image;
        """
    @abstractmethod
    def remove_geometry(self) -> None:
        """Removes the current geometry"""
    @abstractmethod
    def set_geometry_png(self, png: bytes) -> None:
        """Defines the geometry of this drum based on the given PNG image.
        Args:
            png: A bytes object representing the PNG image
        """
    @abstractmethod
    def set_geometry_cli(self, cli_file: bytes) -> None:
        """Defines the geometry of this drum based on the given CLI file.
        Args:
            cli_file: A bytes object representing the CLI file
        """
```

## File: recoater/executor.py/executor.py
```python
"""Module defining an executor class that can start and stop a procedure.
This is used for the deposition procedure and the printing procedure.
It uses the multiprocessing package to start a subprocess with the procedure.
"""
from multiprocessing import Process, Queue
from collections.abc import Callable
from alibrary.logger import logger
class ProcedureExecutor:
    """Class that can execute and cancel its given procedure.
    Attributes:
        procedure: The procedure to run in a subprocess
        cancel_procedure: The procedure to run when cancelling
    """
    def __init__(
        self,
        name: str,
        procedure: Callable[[Queue], None],
        cancel_procedure: Callable[[], None],
    ) -> None:
        self.name = name
        self.process: Process = None
        self.exception_queue = Queue()
        self.procedure = procedure
        self.cancel_procedure = cancel_procedure
    def start(self):
        """Starts the procedure in a subprocess."""
        logger.info("%s procedure started", self.name)
        self.process = Process(target=self.procedure,
                               args=(self.exception_queue,))
        self.process.start()
    def stop(self):
        """Stops the procedure."""
        if self.process is not None and self.process.is_alive():
            self.process.kill()
            self.process = None
        self.cancel_procedure()
        logger.info("%s procedure cancelled", self.name)
    def is_running(self) -> bool:
        """Checks if the procedure is running.
        Returns:
            A boolean flag indicating if the procedure is running or not
        """
        if self.process is None:
            return False
        return self.process.is_alive()
    def has_errors(self) -> bool:
        """Checks if the subprocess has encountered errors.
        Returns:
            A boolean flag indicating if the subprocess had errors
        """
        return not self.exception_queue.empty()
```

## File: recoater/layer/__init__.py/__init__.py
```python
"""Module describing layer related classes"""
from alibrary.recoater.layer.layer import Layer
from alibrary.recoater.layer.parameters import LayerParameters
__all__ = [
    "Layer",
    "LayerParameters",
]
```

## File: recoater/layer/layer.py/layer.py
```python
"""Module describing a Layer class containing the parameters and preview of the
current layer.
"""
import cv2
import matplotlib.colors as mc
import numpy as np
from alibrary.recoater.config import RecoaterConfig
from alibrary.recoater.layer.parameters import LayerParameters
class Layer:
    """Class containing the current layer parameters and able to generate a
    preview from drum's geometries.
    """
    def __init__(self) -> None:
        self.parameters = LayerParameters()
        self.odd_lines = False
    # @staticmethod
    # def __process_powders(geometries: np.ndarray):
    #     """Constructs new geometries by dilating the current ones and by
    #     assigning each pixel to only one powder.
    #     Args:
    #         geometries: A 3D array containing the geometry of each drum
    #     Returns:
    #         A ndarray with the new geometries
    #     """
    #     # Build mask
    #     mask = np.clip(np.sum(geometries, axis=0), 0, 1, dtype=np.uint8)
    #     kernel = np.ones((3, 3), np.uint8)
    #     mask = cv2.dilate(mask, kernel, iterations=3)
    #     # Compute distance of every pixel to the nearest non-zero one
    #     distance_maps = np.zeros(geometries.shape)
    #     for i, geo in enumerate(geometries):
    #         distance_maps[i] = cv2.distanceTransform(
    #             1 - geo, cv2.DIST_L2, maskSize=cv2.DIST_MASK_PRECISE)
    #     # Assign powder
    #     argmin_of_distance = distance_maps.argmin(axis=0)
    #     for i in range(geometries.shape[0]):
    #         geometries[i] = (argmin_of_distance == i) * mask
    def __fill_build_space(self, geometries: np.ndarray):
        """Constructs new geometries taking the filling into account.
        This will add the filling pixel to the corresponding drum.
        Args:
            geometries: A 3D array containing the geometry of each drum
        Returns:
            A ndarray with the new geometries
        """
        mask = np.sum(geometries, axis=0).astype(np.uint8).clip(0, 1)
        kernel = np.ones((3, 3), np.uint8)
        mask = cv2.dilate(mask, kernel, iterations=3)
        # Compute distance of every pixel to the nearest non-zero one
        distance_maps = np.zeros(geometries.shape)
        for i, geo in enumerate(geometries):
            distance_maps[i] = cv2.distanceTransform(
                1 - geo, cv2.DIST_L2, maskSize=cv2.DIST_MASK_PRECISE)
        # Assign powder
        argmin_of_distance = distance_maps.argmin(axis=0)
        for i in range(geometries.shape[0]):
            geometries[i] = (argmin_of_distance == i) * mask
        filling = 1 - mask
        if self.parameters.powder_saving:
            start = 1 if self.odd_lines else 0
            filling[start::2, :] = 0
            self.odd_lines = not self.odd_lines
        geometries[self.parameters.filling_drum_id] += filling
    @staticmethod
    def __apply_powder_offsets(geometries: np.ndarray,
                               powder_offsets: list[int]):
        """Applies the powder offsets on each drum deposition matrix.
        """
        kernel = np.ones((1, 3), np.uint8)
        for i in range(geometries.shape[0]):
            geometries[i] = cv2.dilate(geometries[i],
                                       kernel,
                                       iterations=powder_offsets[i])
    @staticmethod
    def apply_build_plate_dimensions(geometries: np.ndarray,
                                     config: RecoaterConfig):
        """Applies a mask on the given geometries to make them fit the build
        space size.
        Args:
            geometries: The geometries to modify
            config: The recoater configuration containing the build plate
            dimensions
        """
        gw, gl = geometries[0].shape
        if config.build_plate.has_length_and_width():
            bw = int(config.build_plate.width * 1000 / config.resolution)
            bl = int(config.build_plate.length * 1000 / config.resolution)
            mw = (gw - bw) // 2
            ml = (gl - bl) // 2
            mask = np.zeros((gw, gl))
            mask[mw:mw + bw, ml:ml + bl] = 1
            mask = mask == 1
        elif config.build_plate.has_diameter():
            # Circular mask
            cw, cl = gw // 2, gl // 2
            y, x = np.ogrid[:gw, :gl]
            dist_from_center = np.sqrt((y - cw)**2 + (x - cl)**2)
            mask = dist_from_center <= 1 / 2 * (config.build_plate.diameter *
                                                1000 / config.resolution)
        else:
            mask = np.ones((gw, gl))
            mask = mask == 1
        geometries[:, ~mask] = 0
    def get_depositions(self, geometries: np.ndarray, config: RecoaterConfig,
                        powder_offsets: list[int]) -> np.ndarray:
        """Returns the powder depositions matrices.
        It compiled all the drum geometries and add the filling powder. It then
        resizes the depositions using the size of the build plate.
        Args:
            geometries: An array of drum geometry
            config: The recoater config providing the build plate size and the
            resolution
            powder_offsets: A list of powder offsets to apply to the drums
            geometries
        Returns:
            A powder deposition matrix of 3 dimensions with all the individual
            drum deposition
        """
        new_geometries = np.copy(geometries)
        # Add filling and shell
        if self.parameters.filling_drum_id != -1:
            self.__fill_build_space(geometries=new_geometries)
        # Add powder offsets
        self.__apply_powder_offsets(geometries=new_geometries,
                                    powder_offsets=powder_offsets)
        # Apply build plate size
        self.apply_build_plate_dimensions(new_geometries, config)
        return new_geometries
    @staticmethod
    def get_preview(depositions: np.ndarray) -> bytes:
        """Generates and returns a BGR image representing the given depositions
        matrix.
        Args:
            depositions: A matrix with all the drum powder deposition
        Returns:
            A bytes object representing the BGR PNG image
        """
        # Build image canvas
        n_drums = depositions.shape[0]
        width = depositions.shape[1]
        length = depositions.shape[2]
        image = np.zeros((width, length, 4))
        # Copy colors from Matplotlib tableau colors
        preview_colors = np.array(
            [[e * 255 for e in c] for c in map(mc.to_rgba, mc.TABLEAU_COLORS)],
            dtype=np.uint8)
        # RGBA to BGRA conversion
        preview_colors[:, [0, 2]] = preview_colors[:, [2, 0]]
        # Apply colors
        for index in range(n_drums):
            for channel in range(4):
                image[:, :, channel] += depositions[index] * preview_colors[
                    index][channel]
        # Converts numpy array into PNG bytes string
        png = cv2.imencode(".png", image)[1].tobytes()
        return png
```

## File: recoater/layer/parameters.py/parameters.py
```python
"""Module describing the parameters of a layer."""
class LayerParameters:
    """List of the parameters specific to each layer.
    Attributes:
        filling_drum_id: The id of the drum with the filling material
        speed: The speed of the patterning
        x_offset: The pattern offset along the X axis
        powder_saving: A flag indicating if the powder saving techniques should
        be apply
        max_x_offset
    """
    def __init__(self,
                 filling_drum_id: int = -1,
                 speed: float = 0,
                 x_offset: float = 0,
                 powder_saving: bool = False) -> None:
        self.filling_drum_id = filling_drum_id
        self.speed = speed
        self.x_offset = x_offset
        self.powder_saving = powder_saving
        # REVIEW: What value ? Where to initialize ? Is it really useful ?
        self.max_x_offset = 1000
    @classmethod
    def from_json(cls, json: dict[str,]) -> "LayerParameters":
        """Deserializes a JSON object.
        This method returns a LayerParameters object based on th given JSON.
        Args:
            json: The JSON object to deserialize
        Returns:
            A LayerParameters object
        """
        filling_id = int(json["filling_id"]) if "filling_id" in json else 0
        speed = float(json["speed"]) if "speed" in json else 0.0
        x_offset = float(json["x_offset"]) if "x_offset" in json else 0.0
        powder_saving = bool(
            json["powder_saving"]) if "powder_saving" in json else False
        return cls(filling_drum_id=filling_id,
                   speed=speed,
                   x_offset=x_offset,
                   powder_saving=powder_saving)
    def to_json(self) -> dict[str,]:
        """Returns a JSON representation of this LayerParameters.
        Returns:
            A JSON dictionary
        """
        json = {
            "filling_id": self.filling_drum_id,
            "speed": self.speed,
            "x_offset": self.x_offset,
            "max_x_offset": self.max_x_offset,
            "powder_saving": self.powder_saving,
        }
        return json
```

## File: recoater/leveler.py/leveler.py
```python
"""Module describing a class to control the leveler."""
# from alibrary.electronics.controllino import Controllino
# from alibrary.electronics.pcb import PssPCB
from alibrary.logger import logger
from alibrary.pneumatic.valve import PneumaticValve
from alibrary.server import BadRequestError
from alibrary.recoater.drums import Blade
class Leveler:
    """Interface to read and control every aspect of the recoater leveler.
    Attributes:
        max_pressure: The maximum pressure that can be set to the leveler
        target_pressure: The target pressure of this leveler
        pcb: The interface to the underlying pcb
    """
    def __init__(self, valve: PneumaticValve) -> None:
        self.valve = valve
        #     # self.valve = valve
        self.valve.perform_homing()
    @property
    def pressure(self) -> dict[str,]:
        """Returns the current, target and max pressure values of the leveler.
        Returns:
            A JSON object describing the different pressure values
        Raises:
            InternalServerError: An error occurs in the process
        """
        # current = self.get_pressure()
        values = {
            "target": self.valve.target,
            "maximum": self.valve.maximum,
            "value": self.valve.pressure
        }
        return values
    @pressure.setter
    def pressure(self, pressure: dict[str,]) -> None:
        """Sets the requested pressure.
        Args:
            A JSON object describing the target pressure
        Raises:
            InternalServerError: An error occurs in the process
            BadRequestError: The request pressure is invalid
        """
        pressure = pressure["target"]
        # Validation
        if not 0 <= pressure < self.valve.maximum:
            raise BadRequestError(
                f"(leveler) The pressure should be between 0 and"
                f" {self.valve.maximum} but received {pressure}")
        # Application
        self.valve.target = pressure
        logger.info("Leveler suction target pressure set to %f", pressure)
    @staticmethod
    def get_sensor_info() -> dict[str,]:
        """Returns the state of the leveler blade sensor.
        Returns:
            The state of the blade ensor
        Raises:
            InternalServerError: An error occurs in the process
        """
        json = {"state": True}
        return json
    def activate_regulation(self):
        """Activates the pressure regulation of the valve."""
        if self.valve.target != 0:
            self.valve.activate_regulation(self.valve.target)
    def deactivate_regulation(self):
        """Deactivates the pressure regulation of the valve."""
        self.valve.deactivate_regulation()
class LevelerWithBlade(Leveler):
    """Leveler equipped with a motorized blade."""
    def __init__(
        self,
        valve: PneumaticValve,
        blade: Blade,
    ) -> None:
        self._blade: Blade = blade
        super().__init__(valve)
    @property
    def blade(self) -> Blade:
        """The blade added to this leveler."""
        return self._blade
```

## File: recoater/shovels.py/shovels.py
```python
"""Module defining the shovels of a recoater"""
from enum import Enum
from alibrary.electronics import Controllino
from alibrary.logger import logger
class ShovelState(Enum):
    """Types of motion"""
    DISABLED = 0
    OPEN = 1
    CLOSE = 2
class Shovels:
    """Interface to control the shovels of the recoater"""
    def __init__(self, plc: Controllino) -> None:
        self.plc = plc
    def get_state(self) -> dict[str,]:
        """Returns the state of the shovels
        Returns:
            A JSON object describing the state of the shovels
        Raises:
            InternalServerError: An error occurs in the process
        """
        state = ShovelState(self.plc.get_shovels_state())
        return {
            "state": state.name.lower(),
        }
    def set_state(self, json: dict[str,]) -> None:
        """Sets the state of the shovels
        Args:
            A JSON object describing the state of the shovels
        Raises:
            InternalServerError: An error occurs in the process
        """
        if "state" in json:
            state = ShovelState[str(json["state"]).upper(
            )] if "state" in json else ShovelState.DISABLED
            self.plc.set_shovels_state(state.value)
            logger.debug("shovels set to %s", state)
```

## File: server.py/server.py
```python
"""Module defining custom exceptions and helper methods to manage the server's
errors.
This made this package dependent on Flask as a web server but it ease the
creation of server for new projects.
"""
import json
from connexion.exceptions import ProblemException
from flask import Flask, Response
from flask_cors import CORS
from werkzeug.exceptions import HTTPException
from alibrary.logger import logger
class CustomHttpError(RuntimeError):
    """Custom HTTP error raised by the server.
    This class will be specified to cover typical HTTP error code.
    """
    def __init__(self, message: str, code: int) -> None:
        self.message = message
        self.code = code
        super().__init__(message)
class BadRequestError(CustomHttpError):
    """Error raised in case of Bad Request, when the provided arguments are not
    valid
    """
    def __init__(self, message: str) -> None:
        super().__init__(f"[BAD REQUEST] {message}", 400)
class NotFoundError(CustomHttpError):
    """Error raised in case of Not Found, when the requested resource does not
    exist
    """
    def __init__(self, message: str) -> None:
        super().__init__(f"[NOT FOUND] {message}", 404)
class ConflictError(CustomHttpError):
    """Error raised in case of Conflict, when the request conflicts with the
    current server state
    """
    def __init__(self, message: str) -> None:
        super().__init__(f"[CONFLICT] {message}", 409)
class InternalServerError(CustomHttpError):
    """Error raised in case of Internal Server Error, when the server
    encounters a runtime error
    """
    def __init__(self, message: str) -> None:
        super().__init__(f"[INTERNAL SERVER ERROR] {message}", 500)
def custom_error_handler(error) -> Response:
    """Handles an error and returns the appropriate Flask Response.
    Args:
        error: An exception thrown by the server
    Returns:
        A Response object with the error message
    """
    if isinstance(error, CustomHttpError):
        return Response(
            response=json.dumps({
                "status_code": error.code,
                "message": error.message
            }),
            status=error.code,
            mimetype="application/json",
        )
    if isinstance(error, HTTPException):
        return Response(
            response=json.dumps({
                "status_code": error.code,
                "message": error.description
            }),
            status=error.code,
            mimetype="application/json",
        )
    if isinstance(error, ProblemException):
        return Response(
            response=json.dumps({
                "status_code":
                error.status,
                "message":
                f"[{error.title.upper()}] {error.detail}"
            }),
            status=error.status,
            mimetype="application/json",
        )
    return Response(
        response=json.dumps({
            "status_code": 500,
            "message": "UNKNOWN ERROR"
        }),
        status=500,
        mimetype="application/json",
    )
def enable_cors(app: Flask):
    """Initializes the CORS policy."""
    CORS(app)
    logger.debug("CORS enabled")
```
