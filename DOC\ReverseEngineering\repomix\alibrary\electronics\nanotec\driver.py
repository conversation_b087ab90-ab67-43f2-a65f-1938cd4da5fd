"""Module describing a generic Nanotec motor driver.

This will be specialized to adjust to every kind of motor we use with a Nanotec
driver.
"""
# import struct
import time

from alibrary.electronics.modbus import ModbusComponent, ModbusError
from alibrary.electronics.nanotec.state import NanotecDriverState
from alibrary.logger import logger


class NanotecDriverError(Exception):
    """Exception raised when an error occurs in the communication with the
    Nanotec driver.
    """


class NanotecDriver(ModbusComponent):
    """Generic Nanotec driver

    Interface for any Nanotec driver used by Aerosint. It implements both
    ModbusComponent and Motor classes.
    """
    # Statusword of the Nanotec driver (6041)
    STATUS_WORD_ADDRESS = 2000
    # Controlword of the Nanotec driver (6040)
    CONTROL_WORD_ADDRESS = 3000
    # Address of the information out register
    READ_INFORMATION_ADDRESS = 2014

    # Modes of Operation of the Nanotec driver (6060)
    OPERATION_MODE_READ_ADDRESS = 2002
    # Modes of Operation Display of the Nanotec driver (6061)
    OPERATION_MODE_WRITE_ADDRESS = 3002

    # Address of the target position register
    TARGET_POSITION_ADDRESS = 3006
    # Address of the target speed register
    TARGET_SPEED_ADDRESS = 3008
    # Address of the target acceleration register
    TARGET_ACCELERATION_ADDRESS = 3010
    # Address of the target deceleration register
    TARGET_DECELERATION_ADDRESS = 3012

    # Address of the current position register
    ACTUAL_POSITION_ADDRESS = 2008
    # Address of the current speed register
    ACTUAL_SPEED_ADDRESS = 2010

    # Address of the speed register when searching the zero
    SEARCH_ZERO_SPEED_ADDRESS = 3016
    # Speed when searching the zero [µm/s]
    SEARCH_ZERO_SPEED = 3000

    # Hall sensor address
    SENSOR_ADDRESS = 2012

    # Address of the homing method index
    HOMING_METHOD_ADDRESS = 3018
    # Address of the homing position
    HOMING_OFFSET_ADDRESS = 3020
    # Homing operation mode
    HOMING_MODE = 6
    # Start homing without moving control word
    START_MOTIONLESS_HOMING = 0x17

    # Default control word value
    DEFAULT_CONTROL_WORD = 0x0
    # Start motion control word
    START_MOTION_CONTROL_WORD = 0xF

    def __init__(self,
                 ip: str,
                 port: int = 502,
                 timeout: int = 2,
                 offline: bool = False) -> None:
        super().__init__(ip, port, timeout, offline)

        self.initialization_sequence()

    def initialization_sequence(self):
        """
        Carries out the initialization procedure (cancels faults, disables,
        sets to ready, switches on).
        """
        try:
            # Initialization sequence of the driver
            if not self.offline:
                if self.has_fault():
                    self.reset_fault()

                self.__set_state_switch_on_disabled()
                self.__set_state_ready_to_switch_on()
                self.__set_state_switched_on()
                logger.info("Nanotec driver (%s:%d) successfully started",
                            self.ip, self.port)
        except NanotecDriverError as error:
            logger.error("Could not initialize Nanotec driver: %s", error)

    def __get_state(self) -> NanotecDriverState:
        """Retrieves the current state of the Nanotec driver.

        Returns:
            A NanotecDriverState object

        Raises:
            NanotecDriverError: An error occurs during the reading of the
            status word.
        """
        if self.offline:
            return NanotecDriverState.SWITCHED_ON
        try:
            status_word = self.read_registers(self.STATUS_WORD_ADDRESS)
            state = NanotecDriverState.from_status_word(status_word=status_word)
            logger.debug("(Nanotec driver) Read status %s", state)
            return state
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError(str(error)) from error

    def __set_control_word(self, value: int):
        """Sets the control word of the Nanotec driver.

        Args:
            value: The value to set as the control word

        Raises:
            NanotecDriverError: An error occurs during the writing of the
            control word.
        """
        if not self.offline:
            try:
                self.write_registers(self.CONTROL_WORD_ADDRESS, value)
            except ModbusError as error:
                logger.error(str(error))
                raise NanotecDriverError(str(error)) from error

    def has_fault(self) -> bool:
        """Checks if the driver is in FAULT state.

        Returns:
            A boolean indicating if the driver is in FAULT state or not

        Raises:
            NanotecDriverError: An error occurs while getting the state.
        """
        return self.__get_state() == NanotecDriverState.FAULT

    def reset_fault(self):
        """Resets the FAULT state of the driver

        Raises:
            NanotecDriverError: An error occurs while resetting fault.
        """
        reset_fault_control_word = 0x80

        self.__set_control_word(reset_fault_control_word)

        self.__wait_for_state(NanotecDriverState.SWITCH_ON_DISABLED)

        self.__set_control_word(self.DEFAULT_CONTROL_WORD)

    def __wait_for_state(self, state: NanotecDriverState):
        """Waits until the given state is the current state of the driver.

        Args:
            state: The state to wait

        Raises:
            NanotecDriverError: An error occurs while waiting the state.
        """
        cnt = 0
        loop_time = 0.01
        while self.__get_state() != state:
            cnt += 1

            if cnt > self.timeout // loop_time:
                logger.error("Timeout waiting state %s", state)
                raise NanotecDriverError(f"Timeout waiting state {state}")
            time.sleep(loop_time)

    def __set_state_switch_on_disabled(self):
        """Sets the driver's state to SWITCH ON DISABLED.

        Raises:
            NanotecDriverError: An error occurs while setting the state.
        """
        current_state = self.__get_state()

        if current_state == NanotecDriverState.SWITCH_ON_DISABLED:
            return

        if current_state == NanotecDriverState.FAULT:
            self.reset_fault()
            return

        if current_state == NanotecDriverState.FAULT_REACTION_ACTIVE:
            raise NanotecDriverError("Could not set state SWITCH ON DISABLED")

        self.__set_control_word(self.DEFAULT_CONTROL_WORD)

        self.__wait_for_state(NanotecDriverState.SWITCH_ON_DISABLED)

    def __set_state_ready_to_switch_on(self):
        """Sets the driver's state to READY TO SWITCH ON.

        Raises:
            NanotecDriverError: An error occurs while setting the state.
        """
        request_ready_to_switch_on_state = 0x06
        current_state = self.__get_state()

        if current_state == NanotecDriverState.READY_TO_SWITCH_ON:
            return

        if current_state in (NanotecDriverState.SWITCH_ON_DISABLED,
                             NanotecDriverState.SWITCHED_ON,
                             NanotecDriverState.OPERATION_ENABLED):
            self.__set_control_word(request_ready_to_switch_on_state)
            self.__wait_for_state(NanotecDriverState.READY_TO_SWITCH_ON)
            return

        raise NanotecDriverError("Could not set state READY TO SWITCH ON")

    def __set_state_switched_on(self):
        """Sets the driver's state to SWITCHED ON.

        Raises:
            NanotecDriverError: An error occurs while setting the state.
        """
        request_switched_on_state = 0x07
        current_state = self.__get_state()

        if current_state == NanotecDriverState.SWITCHED_ON:
            return

        if current_state in (NanotecDriverState.READY_TO_SWITCH_ON,
                             NanotecDriverState.OPERATION_ENABLED):
            self.__set_control_word(request_switched_on_state)
            self.__wait_for_state(NanotecDriverState.SWITCHED_ON)
            return

        raise NanotecDriverError("Could not set state SWITCHED ON")

    def __wait_for_operation_mode(self, mode: int):
        """Waits for the operation mode to change to the specified value."""
        cnt = 0
        while self.read_registers(self.OPERATION_MODE_READ_ADDRESS) != mode:
            cnt += 1

            if cnt > 100:
                logger.error("Timeout waiting operation mode %d", mode)
                raise NanotecDriverError(
                    f"Timeout waiting operation mode {mode}")
            time.sleep(0.01)

    def __set_operation_mode(self, mode: int):
        """Sets the operation mode of the driver.

        Args:
            mode: An integer representing the mode of operation

        Raises:
            NanotecDriverError: An error occurs while setting the operation
            mode.
        """
        try:
            if not self.offline:
                self.write_registers(self.OPERATION_MODE_WRITE_ADDRESS, mode)
                self.__wait_for_operation_mode(mode)
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError(str(error)) from error

    def __get_bit(self, address: int, bit_index: int) -> bool:
        """Returns one bit from the Nanotec driver register at the given
        address.

        Args:
            address: The address of the registers to read.
            bit_index: The index of the bit to check, starting at zero

        Raises:
            NanotecDriverError: An error occurs while checking bit of status
            word.
        """
        if self.offline:
            return False

        try:
            status_word = self.read_registers(address)
            return int(status_word / 2**bit_index) % 2 == 1
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError(str(error)) from error

    def __check_state_for_move(self):
        state = self.__get_state()
        if state not in (NanotecDriverState.SWITCHED_ON,
                         NanotecDriverState.OPERATION_ENABLED):
            err_str = f"Impossible to perform Nanotec motion from state {state}"
            logger.error(err_str)
            raise NanotecDriverError(err_str)

    def __read(self, address: int) -> int:
        """Reads a register of the Nanotec driver;

        Args:
            address: The address of the register to read

        Returns:
            The 32 bits integer stored inside the register

        Raises:
            NanotecDriverError: An error occurs while reading the register
        """
        if self.offline:
            return 0

        try:
            return self.read_registers(address)
        except ModbusError as error:
            logger.error(str(error))
            raise NanotecDriverError("READ " + str(error)) from error

    def __write(self, address: int, value: int):
        """Writes a register of the Nanotec driver;

        Args:
            address: The address of the register to write
            value: The integer value to write

        Raises:
            NanotecDriverError: An error occurs while writing the register
        """
        if not self.offline:
            try:
                self.write_registers(address, value)
            except ModbusError as error:
                logger.error(str(error))
                raise NanotecDriverError("WRITE " + str(error)) from error

    def is_busy(self) -> bool:
        """Returns the running status of the motor.

        This is stored in the first bit of the READ_INFORMATION register.

        Returns:
            True if a motion is running on the motor, false otherwise

        Raises:
            NanotecDriverError: AAn error occurs in the process
        """
        return self.__get_bit(self.READ_INFORMATION_ADDRESS, 0)

    def is_homed(self) -> bool:
        """Returns the homing status of the motor.

        This is stored in the second bit of the READ_INFORMATION register.

        Returns:
            True if a motion is homing on the motor, false otherwise

        Raises:
            NanotecDriverError: AAn error occurs in the process
        """
        return self.__get_bit(self.READ_INFORMATION_ADDRESS, 1)

    def get_position(self) -> float:
        """Returns the current position.

        Returns:
            The current position in µm

        Raises:
            NanotecDriverError: An error occurs in the process
        """
        return self.__read(self.ACTUAL_POSITION_ADDRESS)

    def get_speed(self) -> float:
        """Returns the current speed.

        Returns:
            The current speed in µm/s

        Raises:
            NanotecDriverError: An error occurs in the process
        """
        return self.__read(self.ACTUAL_SPEED_ADDRESS)

    def perform_speed_motion(self, speed: int, acceleration: int):
        """Performs a speed motion.

        Args:
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²
        Raises:
            NanotecDriverError: AAn error occurs in the process
        """
        speed_mode = 3

        self.__check_state_for_move()

        self.__write(self.TARGET_SPEED_ADDRESS, speed)
        self.__write(self.TARGET_ACCELERATION_ADDRESS, acceleration)

        self.__set_operation_mode(speed_mode)
        self.__set_control_word(self.START_MOTION_CONTROL_WORD)

    def perform_distance_motion(self,
                                distance: int,
                                speed: int,
                                acceleration: int,
                                deceleration: int,
                                is_relative=True):
        """
        Performs a distance motion.

        It can either be relative or absolute.

        Args:
            distance: The distance to travel in µm
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²
            deceleration: The acceleration in µm/s²
            is_relative: A flag indicating if the distance is relative or not

        Raises:
            NanotecDriverError: An error occurs in the process
        """
        distance_mode = 1

        self.__check_state_for_move()

        self.__write(self.TARGET_POSITION_ADDRESS, distance)
        self.__write(self.TARGET_SPEED_ADDRESS, speed)
        self.__write(self.TARGET_ACCELERATION_ADDRESS, acceleration)
        self.__write(self.TARGET_DECELERATION_ADDRESS, deceleration)

        if is_relative:
            oms = 0b111
        else:
            oms = 0b011
        control_word = oms * 16 + 0xF

        self.__set_operation_mode(distance_mode)
        self.__set_control_word(self.START_MOTION_CONTROL_WORD)
        self.__set_control_word(control_word)

        target_reached_bit_index = 12
        while not self.__get_bit(self.STATUS_WORD_ADDRESS,
                                 target_reached_bit_index):
            time.sleep(0.1)

        # Remove 5th bit
        control_word -= 16
        self.__set_control_word(control_word)

    def perform_homing(self, speed: int, acceleration: int,
                       search_zero_speed: int):
        """Performs the homing procedure.

        Args:
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²

        Raises:
            NanotecDriverError: An error occurs in the process
        """
        full_homing_method = 17
        start_homing_control_word = 0x1F

        self.__check_state_for_move()

        self.__write(self.HOMING_OFFSET_ADDRESS, 0)
        self.__write(self.HOMING_METHOD_ADDRESS, full_homing_method)
        self.__write(self.SEARCH_ZERO_SPEED_ADDRESS, search_zero_speed)
        self.__write(self.TARGET_SPEED_ADDRESS, speed)
        self.__write(self.TARGET_ACCELERATION_ADDRESS, acceleration)

        self.__set_operation_mode(self.HOMING_MODE)
        self.__set_control_word(self.START_MOTION_CONTROL_WORD)
        self.__set_control_word(start_homing_control_word)

    def perform_custom_homing(self, speed: int, acceleration: int):
        """Performs the custom homing procedure.

        The motion is started in speed mode and stopped when the sensor is
        triggered.

        Args:
            speed: The speed in µm/s
            acceleration: The acceleration in µm/s²

        Raises:
            NanotecDriverError: An error occurs in the process
        """
        self.__check_state_for_move()

        self.perform_speed_motion(speed, acceleration)

        while not self.__read(self.SENSOR_ADDRESS) == 1:
            time.sleep(0.01)

        self.stop(acceleration)

        self.__set_operation_mode(self.HOMING_MODE)
        self.__set_control_word(self.START_MOTIONLESS_HOMING)

    def perform_position_homing(self, position: int):
        """Performs a homing operation that set the actual position without
        moving.

        Args:
            position: The position to set in µm

        Raises:
            NanotecDriverError: An error occurs in the process
        """
        position_homing_method = 35

        self.__check_state_for_move()

        self.__write(self.HOMING_METHOD_ADDRESS, position_homing_method)
        self.__write(self.HOMING_OFFSET_ADDRESS, position)

        self.__set_operation_mode(self.HOMING_MODE)
        self.__set_control_word(self.START_MOTIONLESS_HOMING)

    def stop(self, deceleration: int):
        """Stops the currently running motion.

        Args:
            deceleration: The acceleration in µm/s²

        Raises:
            NanotecDriverError: An error occurs in the process
        """
        stop_control_word = 0x7

        self.__write(self.TARGET_DECELERATION_ADDRESS, deceleration)
        self.__set_control_word(stop_control_word)

    def halt(self):
        """Stops the currently running motion.

        It sends a HALT to the Nanotec driver.
        """
        halt_control_word = 0x10F

        self.__set_control_word(halt_control_word)
