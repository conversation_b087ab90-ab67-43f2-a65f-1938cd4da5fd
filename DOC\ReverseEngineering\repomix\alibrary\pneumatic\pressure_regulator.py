"""Module describing a pressure regulator class."""
from abc import ABC, abstractmethod

class PressureRegulator(ABC):
    """A component that can regulate a pressure.
    """
    def __init__(self, maximum: float) -> None:
        self._pressure: float = 0
        self._target: float = 0
        self._maximum: float = maximum

    @property
    def maximum(self) -> float:
        """Getter for the maximum pressure"""
        return self._maximum

    @maximum.setter
    def maximum(self, maximum: float) -> None:
        """Setter for the maximum pressure"""
        self._maximum = maximum

    @property
    @abstractmethod
    def pressure(self) -> float:
        """Returns the current pressure.

        Returns:
            A float representing the current pressure.
        """

    @property
    def target(self) -> float:
        """Returns the current target pressure.

        Returns:
            A float representing the current target pressure.
        """
        return self._target

    @target.setter
    @abstractmethod
    def target(self, pressure: float) -> None:
        """Sets the target pressure."""
