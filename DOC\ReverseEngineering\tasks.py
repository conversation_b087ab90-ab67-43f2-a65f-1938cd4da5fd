"""Module defining functions used by the server of the Scarlett test bench.

It defines the printing procedure and a cancel procedure that will be run on
cancel event.
"""
import time
from multiprocessing import Queue
from threading import Thread
import requests

from alibrary.motions import MotionType
from alibrary.motions.nanotec import NanotecMotionCommand
from alibrary import logger
from alibrary.server import CustomHttpError

from . import models as m
from . import config


class CancellationException(Exception):
    """Raised when print procedure should cancel"""


def print_procedure(queue: Queue):
    """Executes the printing procedure of the Scarlett test bench.

    It uses a Queue to send to the main process the errors it encountered.

    Args:
        queue: The error queue between the processes
    """
    try:
        base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"

        layer_range = range(m.printer.start_layer_index,
                            m.printer.end_layer_index + 1)

        for crt_layer in layer_range:
            # Move z down
            z_motion_down = NanotecMotionCommand(
                motion_type=MotionType.RELATIVE,
                speed=m.z_axis.speed,
                distance=-m.z_axis.offset)
            m.z_axis.start_motion(z_motion_down)
            Thread(target=m.axis_position.moving).start()

            while m.z_axis.motor.is_busy():
                time.sleep(0.1)

            # Move head to the right
            x_motion_right = NanotecMotionCommand(
                motion_type=MotionType.ABSOLUTE,
                speed=m.x_axis.travel_speed,
                distance=config.X_RIGHT_PRINT_POSITION)
            m.x_axis.start_motion(x_motion_right)

            while m.x_axis.motor.is_busy():
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)

            # Move z up
            z_motion_up = NanotecMotionCommand(motion_type=MotionType.RELATIVE,
                                               speed=m.z_axis.speed,
                                               distance=m.z_axis.offset -
                                               m.z_axis.layer_thickness)
            m.z_axis.start_motion(z_motion_up)
            Thread(target=m.axis_position.moving).start()

            while m.z_axis.motor.is_busy():
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)

            # Put drums geometries
            for d in range(config.N_DRUMS):
                layer = m.printer.get_layer_for_drum(crt_layer, d)

                requests.put(
                    f"{base_url}/drums/{d}/geometry",
                    data=layer,
                    headers={"Content-Type": "application/octet-stream"},
                    timeout=10.0)

            # Start print job
            requests.post(f"{base_url}/print/job", timeout=3.0)

            time.sleep(1)

            # Move head to the left
            x_motion_left = NanotecMotionCommand(
                motion_type=MotionType.ABSOLUTE,
                speed=m.x_axis.patterning_speed,
                distance=config.X_LEFT_PRINT_POSITION)
            m.x_axis.start_motion(x_motion_left)

            while m.x_axis.motor.is_busy():
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)

            # While printing, sleep
            response = requests.get(f"{base_url}/state", timeout=3.0)
            while response.json()["state"] == "printing":
                if should_cancel():
                    raise CancellationException()
                time.sleep(0.1)
                response = requests.get(f"{base_url}/state", timeout=3.0)

            # Move head to left limit to trigger collector
            if (m.printer.collectors_delay != 0 and
                    crt_layer % m.printer.collectors_delay == 0):
                x_motion_left = NanotecMotionCommand(
                    motion_type=MotionType.ABSOLUTE,
                    speed=m.x_axis.patterning_speed,
                    distance=config.X_MIN_POSITION)
                m.x_axis.start_motion(x_motion_left)

                while m.x_axis.motor.is_busy():
                    if should_cancel():
                        raise CancellationException()
                    time.sleep(0.1)

            time.sleep(1)
            with m.last_printed_layer_index.get_lock():
                m.last_printed_layer_index.value = crt_layer

    except CustomHttpError as error:
        logger.error("Error during print procedure: %s", str(error))
        queue.put(error)
    except CancellationException:
        print("Cancel required")
        cancel_procedure()


def cancel_procedure():
    """Executes the cancel procedure."""
    m.x_axis.stop_motion()
    base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"
    requests.delete(f"{base_url}/print/job", timeout=3.0)


def get_preview(layer_id):
    """Get the preview of the specified layer"""
    base_url = f"http://{config.ASERVER_ADDRESS}/api/v3"
    # Put drums geometries
    for d in range(config.N_DRUMS):
        layer = m.printer.get_layer_for_drum(layer_id, d)

        if layer is not None:
            requests.put(f"{base_url}/drums/{d}/geometry",
                         data=layer,
                         headers={"Content-Type": "application/octet-stream"},
                         timeout=10.0)
        else:
            requests.delete(f"{base_url}/drums/{d}/geometry", timeout=3.0)

    return requests.get(f"{base_url}/layer/preview",
                        headers={
                            "Content-Type": "image/png"
                        },
                        timeout=3.0).content


def reset():
    """Resets the y driver."""
    logger.info("Resetting the driver of y axis.")
    m.z_driver.initialization_sequence()
    with m.Z_AXIS_POSITION.get_lock():
        position = m.Z_AXIS_POSITION.value
        if position != -1:
            m.z_driver.perform_position_homing(int(position * 1000))


def monitor_door():
    """Watches the door interlock signal"""
    while m.SHOULD_LOOP:
        if not m.controllino.is_door_interlock_closed(
        ) and not m.WAS_DOOR_OPEN.is_set():
            logger.warning("Door interlock opened")
            m.z_driver.client.close()
            m.WAS_DOOR_OPEN.set()
        elif m.controllino.is_door_interlock_closed(
        ) and m.WAS_DOOR_OPEN.is_set():
            time.sleep(7)
            m.z_driver.connect()
            reset()
            time.sleep(1)
            m.WAS_DOOR_OPEN.clear()
        time.sleep(0.2)


def monitor_cover():
    """Watches the cover interlock signal"""
    m.WAS_COVER_OPEN.clear()
    while m.SHOULD_LOOP:
        if not m.controllino.is_cover_interlock_closed(
        ) and not m.WAS_COVER_OPEN.is_set():
            logger.warning("Cover interlock opened")
            m.WAS_COVER_OPEN.set()
        elif m.controllino.is_cover_interlock_closed(
        ) and m.WAS_COVER_OPEN.is_set():
            m.WAS_COVER_OPEN.clear()
        time.sleep(0.2)


def monitor_ems():
    """Watches the EMS signal"""
    m.WAS_EMS_ACTIVATED.clear()
    while m.SHOULD_LOOP:
        if not m.controllino.is_ems_deactivated(
        ) and not m.WAS_EMS_ACTIVATED.is_set():
            logger.warning("EMS activated")
            m.WAS_EMS_ACTIVATED.set()
        time.sleep(0.2)


def should_cancel():
    return m.WAS_EMS_ACTIVATED.is_set() or m.WAS_COVER_OPEN.is_set(
    ) or m.WAS_DOOR_OPEN.is_set()
