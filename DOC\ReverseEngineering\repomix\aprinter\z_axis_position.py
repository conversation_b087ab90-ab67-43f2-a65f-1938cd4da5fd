"""
File containing the class saving the position of an axis.
"""

from time import sleep
from alibrary.electronics.nanotec import NanotecDriverError
from alibrary import logger
from . import models as m


class ZAxisPosition:
    """
    Class monitoring the position of an axis.
    """

    def moving(self):
        """
        Waits for an axis to stop to retrieve its position.
        """
        try:
            with m.Z_AXIS_POSITION.get_lock():
                m.Z_AXIS_POSITION.value = -1

                self.wait_finished()
                sleep(1)
                m.Z_AXIS_POSITION.value = m.z_axis.motor.get_position()
        except NanotecDriverError as e:
            logger.warning(str(e))

    def wait_finished(self):
        """
        Waits for the axis to stop moving.
        """
        running = True
        while running:
            if (m.controllino.is_door_interlock_closed() and
                    m.controllino.is_ems_deactivated()):
                running = m.z_axis.motor.is_busy()
                sleep(0.1)
            elif not m.controllino.is_door_interlock_closed():
                m.WAS_DOOR_OPEN.set()
                raise NanotecDriverError("Door interlock opened")
            elif not m.controllino.is_ems_deactivated():
                m.WAS_EMS_ACTIVATED.set()
                raise NanotecDriverError("EMS activated")
