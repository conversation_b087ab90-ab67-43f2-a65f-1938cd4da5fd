"""Module defining an interface to a physical STM32 PLC."""
import socket
import struct
import time
from multiprocessing import Lock

from alibrary.electronics.stm32.packet import STM32Packet
from alibrary.electronics.stm32.state import STM32State
from alibrary.electronics.ethernet import <PERSON>ther<PERSON><PERSON>omponent
from alibrary.logger import logger


class STM32Error(Exception):
    """Exception raised when an error occurs in the communication with the
    STM32.
    """


class STM32(EthernetComponent):
    """Interface to a physical STM32."""

    def __init__(
        self,
        ip: str,
        port: int,
        timeout: int = 2,
        offline: bool = False,
    ) -> None:
        super().__init__(ip, port, timeout, offline)

        self.lock = Lock()

        if not self.offline:
            # Communication socket
            self.__communication_socket = socket.socket(socket.AF_INET,
                                                        socket.SOCK_STREAM)
            self.__communication_socket.settimeout(self.timeout)
            try:
                self.__communication_socket.connect((self.ip, self.port))
                self.__send(self.__communication_socket, 0)
                self.__read(self.__communication_socket)
            except socket.timeout as error:
                logger.error(
                    "(STM32) Timeout while opening communication socket")
                raise STM32Error(str(error)) from error
            except socket.error as error:
                logger.error("(STM32) Error while opening communication socket")
                raise STM32Error(str(error)) from error

            # Communication socket
            self.__printing_socket = socket.socket(socket.AF_INET,
                                                   socket.SOCK_STREAM)
            self.__printing_socket.settimeout(20)
            try:
                self.__printing_socket.connect((self.ip, self.port))
                self.__send(self.__printing_socket, 1)
                self.__read(self.__printing_socket)
            except socket.timeout as error:
                logger.error("(STM32) Timeout while opening printing socket")
                raise STM32Error(str(error)) from error
            except socket.error as error:
                logger.error("(STM32) Error while opening printing socket")
                raise STM32Error(str(error)) from error

    def __send(self,
               soc: socket.socket,
               value: int,
               n_bytes: int = 1,
               signed=False) -> None:
        """Sends the given value in its bytes representation to the given socket.

        Args:
            value: The integer to convert
            n_bytes: The number of bytes in the result

        Returns:
            A bytes object representing the value
        """
        soc.sendall(value.to_bytes(n_bytes, byteorder="little", signed=signed))

    def __send_float(self, soc: socket.socket, value: float):
        """Sends a float to the given socket"""
        soc.sendall(bytearray(struct.pack("f", value)))

    def __read(self, soc: socket.socket, n_bytes: int = 1, signed=False) -> int:
        """Reads a given number of bytes on the given socket."""
        data = b""
        for _ in range(n_bytes):
            data += soc.recv(1)

        return int.from_bytes(data, byteorder="little", signed=signed)

    def __read_float(self, soc: socket.socket):
        """Reads a float on the given socket."""
        data = b""
        for _ in range(4):
            data += soc.recv(1)

        return struct.unpack("f", data)[0]

    def get_driver_mode(self) -> STM32State:
        """Returns the current driver mode."""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 7)
                    state_received = False
                    while not state_received:
                        try:
                            state = self.__read(self.__communication_socket)
                            state_received = True
                        except socket.timeout:
                            time.sleep(1)
                    return STM32State(state)
                except ValueError as error:
                    logger.error("(STM32) State %d does not exist", state)
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error
        return 0

    def is_printing(self) -> bool:
        """Returns True if the current driver mode is printing."""
        return self.get_driver_mode() != STM32State.IDLE

    def get_ejection_pressure(self, index: int) -> int:
        """Sets the ejection pressure for the given drum.

        Args:
            index: The index of the drum for which to set the ejection
        """
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 2)
                    self.__send(self.__communication_socket, index)
                    return self.__read(self.__communication_socket, n_bytes=2)
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error
        return 0

    def set_ejection_pressure(self, index: int, pressure: int) -> None:
        """Sets the ejection pressure for the given drum.

        Args:
            index: The index of the drum for which to set the ejection
            pressure: The ejection pressure to set in Pa
        """
        enable_ejection_pressure_code = 11
        if not self.offline:
            with self.lock:
                try:
                    # Set pressure
                    self.__send(self.__communication_socket, 1)
                    self.__send(self.__communication_socket, index)
                    self.__send(self.__communication_socket,
                                pressure,
                                n_bytes=2)
                    # Enable Ejection Pressure
                    self.__send(self.__communication_socket,
                                enable_ejection_pressure_code)
                    self.__send(self.__communication_socket, index)
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error

    def disable_ejection_pressure(self, index: int) -> None:
        """Disable ejection pressure.
        """
        disable_ejection_pressure = 12
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket,
                                disable_ejection_pressure)
                    self.__send(self.__communication_socket, index)
                except socket.error as error:
                    logger.error(
                        "(STM32) Error while opening communication socket")
                    raise STM32Error(str(error)) from error

    def send_packet(self, packet: STM32Packet):
        """Sends a custom packet to the STM32.

        Args:
            packet: A STM32Packet object
        """
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__printing_socket, 0)

                    # Header
                    self.__send(self.__printing_socket, packet.n_bytes, 4)
                    self.__send(self.__printing_socket, packet.line_duration, 4)
                    self.__send(self.__printing_socket,
                                packet.time_before_ejection, 4)

                    # Body
                    self.__printing_socket.sendall(packet.data)

                    # Check image is saved
                    status = self.__read(self.__printing_socket)

                    if status != 0:
                        raise STM32Error(
                            "Problem while saving the image in the STM32 memory"
                        )

                except socket.timeout as error:
                    logger.error(
                        "(STM32) Connection timeout while sending a matrix")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while sending a matrix")
                    raise STM32Error(str(error)) from error

        logger.debug("(STM32) Matrix sent to %s", self.ip)

    def set_test_mode(self, test_index: int, period=1000):
        """Set a test mode in the STM32"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 4)
                    self.__send(self.__communication_socket, test_index)
                    self.__send(self.__communication_socket, period, n_bytes=2)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting test mode")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting test mode")
                    raise STM32Error(str(error)) from error

        logger.debug("(STM32) Test mode %s activated", test_index)

    def test_one_valve(self, valve_index: int, period=1000):
        """Set a test mode in the STM32"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 5)
                    self.__send(self.__communication_socket,
                                valve_index,
                                n_bytes=2)
                    self.__send(self.__communication_socket, period, n_bytes=2)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting test mode")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting test mode")
                    raise STM32Error(str(error)) from error

        logger.debug("(STM32) Test valve %s", valve_index)

    def test_valves(self, start_valve: int, end_valve: int, period=1000):
        """Set a test mode in the STM32"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 6)
                    self.__send(self.__communication_socket,
                                start_valve,
                                n_bytes=2)
                    self.__send(self.__communication_socket,
                                end_valve,
                                n_bytes=2)
                    self.__send(self.__communication_socket, period, n_bytes=2)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting test mode")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting test mode")
                    raise STM32Error(str(error)) from error

        logger.debug("(STM32) Test valves [%s, %s]", start_valve, end_valve)

    def stop(self):
        """Stops the ejection
        """
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 3)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while stopping ejection")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while stopping ejection")
                    raise STM32Error(str(error)) from error

        logger.debug("(STM32) STM32 stopped")

    def wait_end_of_print(self):
        """Waits for the STM32 to signal the end of the pattern."""
        while self.is_printing():
            print(self.get_driver_mode())
            time.sleep(1)

    def set_feedback_gain(self, drum_index: int, kp, ki, kd):
        """Sets the PID parameters"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 8)
                    self.__send(self.__communication_socket, drum_index)
                    self.__send_float(self.__communication_socket, kp)
                    self.__send_float(self.__communication_socket, ki)
                    self.__send_float(self.__communication_socket, kd)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting feedback gain")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting feedback gain")
                    raise STM32Error(str(error)) from error

        logger.debug(
            "(STM32) PID parameters (kp=%s, ki=%s, kd=%s) set for drum %s", kp,
            ki, kd, drum_index)

    def set_period_pid(self, period_pid):
        """Sets the period PID"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 9)
                    self.__send(self.__communication_socket,
                                period_pid,
                                n_bytes=4)
                except socket.timeout as error:
                    logger.error("(STM32) Timeout while setting period pid")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while setting period pid")
                    raise STM32Error(str(error)) from error

        logger.debug("(STM32) Period PID set to %s", period_pid)

    def get_actuation_value(self, drum_index):
        """Sets the period PID"""
        if not self.offline:
            with self.lock:
                try:
                    self.__send(self.__communication_socket, 10)
                    self.__send(self.__communication_socket, drum_index)
                    self.__read_float(self.__communication_socket)
                except socket.timeout as error:
                    logger.error(
                        "(STM32) Timeout while getting actuation value")
                    raise STM32Error(str(error)) from error
                except socket.error as error:
                    logger.error("(STM32) Error while getting actuation value")
                    raise STM32Error(str(error)) from error

        logger.debug("(STM32) Actuation value retrieved for drum %s",
                     drum_index)

    def close(self):
        """Closes the communication socket."""
        self.__communication_socket.close()

    def shutdown(self):
        """Shutdowns the communication socket."""
        self.__communication_socket.shutdown()
