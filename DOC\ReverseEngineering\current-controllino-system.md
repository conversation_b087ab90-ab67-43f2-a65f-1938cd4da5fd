# Current Controllino-Based Recoater System Architecture

This diagram shows the existing system architecture where the Controllino PLC acts as the central control hub for the 3-drum recoater system.

```mermaid
graph TB
    subgraph "External Control System"
        IPC[IPC/Host Computer]
    end
    
    subgraph "Network Infrastructure"
        NET[10.10.192.x Network]
    end
    
    subgraph "Controllino PLC System"
        CTRL_MAIN[Main Controllino PLC<br/>***********:4080]
        CTRL_SEC[Secondary Controllino PLC<br/>***********:4080]
    end
    
    subgraph "Motor Control System"
        NANO_X[Nanotec X-Axis Driver<br/>************:502<br/>Modbus TCP]
        NANO_Z[Nanotec Z-Axis Driver<br/>************:502<br/>Modbus TCP]
        MOTOR_X[X-Axis Motor<br/>Linear Motion]
        MOTOR_Z[Z-Axis Motor<br/>Vertical Motion]
    end
    
    subgraph "3-Drum Recoater Hardware"
        DRUM1[Drum 1<br/>Powder Reservoir]
        DRUM2[Drum 2<br/>Powder Reservoir]
        DRUM3[Drum 3<br/>Powder Reservoir]
        VALVES[384 Valves<br/>Per Drum]
        PRESSURE[Ejection Pressure<br/>Control System]
        GRIPPER[Z-Axis Gripper]
        COLLECTORS[Powder Collectors<br/>& Bridge Breakers]
        PIEZO[Piezo Actuators<br/>Frequency Control]
    end
    
    subgraph "Safety & Monitoring"
        EMS[Emergency Stop<br/>System]
        DOOR[Door Interlocks]
        COVER[Cover Interlocks]
        LIGHT[Chamber Lighting]
    end
    
    %% Network Connections
    IPC -.->|Ethernet| NET
    NET -->|TCP Socket| CTRL_MAIN
    NET -->|TCP Socket| CTRL_SEC
    NET -->|Modbus TCP| NANO_X
    NET -->|Modbus TCP| NANO_Z
    
    %% Motor Control
    NANO_X -->|Control Signals| MOTOR_X
    NANO_Z -->|Control Signals| MOTOR_Z
    MOTOR_Z -->|Mechanical| GRIPPER
    
    %% Controllino to Hardware
    CTRL_MAIN -->|I2C/Digital| PRESSURE
    CTRL_MAIN -->|PWM/Analog| PIEZO
    CTRL_MAIN -->|Digital I/O| VALVES
    CTRL_SEC -->|Digital I/O| COLLECTORS
    CTRL_MAIN -->|Digital I/O| LIGHT
    
    %% Safety Systems
    EMS -->|Safety Circuit| CTRL_MAIN
    DOOR -->|Interlock| CTRL_MAIN
    COVER -->|Interlock| CTRL_MAIN
    
    %% Drum Connections
    PRESSURE -->|Pneumatic| DRUM1
    PRESSURE -->|Pneumatic| DRUM2
    PRESSURE -->|Pneumatic| DRUM3
    VALVES -->|Control| DRUM1
    VALVES -->|Control| DRUM2
    VALVES -->|Control| DRUM3
    
    %% Styling
    classDef controlSystem fill:#e1f5fe
    classDef hardware fill:#f3e5f5
    classDef network fill:#e8f5e8
    classDef safety fill:#fff3e0
    
    class IPC,CTRL_MAIN,CTRL_SEC controlSystem
    class DRUM1,DRUM2,DRUM3,MOTOR_X,MOTOR_Z,GRIPPER hardware
    class NET,NANO_X,NANO_Z network
    class EMS,DOOR,COVER safety
```

## Key Components

### Network Infrastructure
- **10.10.192.x Network**: Central network connecting all components
- **IP Addresses**:
  - Main Controllino: ***********:4080
  - Secondary Controllino: ***********:4080
  - X-Axis Nanotec Driver: ************:502
  - Z-Axis Nanotec Driver: ************:502

### Control Flow
1. **IPC/Host Computer** sends commands over Ethernet
2. **Controllino PLCs** receive commands via TCP sockets
3. **Motor Drivers** receive motion commands via Modbus TCP
4. **Hardware Components** are controlled through various protocols (I2C, PWM, Digital I/O)

### Safety Systems
- Emergency Stop (EMS) system
- Door and cover interlocks
- All safety systems are monitored by the Controllino PLCs
