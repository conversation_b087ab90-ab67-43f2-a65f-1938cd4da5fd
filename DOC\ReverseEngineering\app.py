""""Mo<PERSON><PERSON> defining the app factory function.

This factory function is used by Flask and Connexion to generate the server.
"""
from alibrary import init_logger, logger
from alibrary.server import <PERSON>HttpError, custom_error_handler, enable_cors
from werkzeug.exceptions import HTTPException
from flask import <PERSON>lask
from connexion import <PERSON><PERSON><PERSON><PERSON><PERSON>
from connexion.exceptions import ProblemException
from connexion.resolver import RelativeResolver
from . import COLORED_LOGO, __version__, api_file
from . import config


def create_app() -> Flask:
    """Creates a Connexion application.

    Returns:
        A Flask object
    """
    # Create a FlaskApp object
    connexion_app = FlaskApp(__name__)

    # Add API
    connexion_app.add_api(api_file,
                          resolver=RelativeResolver("aprinter.controllers"),
                          arguments={"title": "Aerosint API"},
                          strict_validation=True,
                          pythonic_params=True,)

    # Change error handlers
    connexion_app.add_error_handler(CustomHttpError, custom_error_handler)
    connexion_app.add_error_handler(ProblemException, custom_error_handler)
    connexion_app.add_error_handler(HTTPException, custom_error_handler)

    # Initialize logger
    init_logger(connexion_app.app)
    enable_cors(connexion_app.app)

    # Print logo
    print(f"{COLORED_LOGO} {config.RECOATER_NAME} v{__version__}\n\n")
    logger.info("Recoater started")

    if config.OFFLINE_MODE:
        logger.warning("Offline mode enabled")

    return connexion_app.app
