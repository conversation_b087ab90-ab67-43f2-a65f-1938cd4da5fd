"""Entry-point of the production server"""
import click
from gunicorn.app.base import BaseApplication
from alibrary.logger import config_logger
from aprinter.app import create_app

from aprinter import __version__


class StandaloneApplication(BaseApplication):
    """Gunicorn application that will run the server"""

    def __init__(self,
                 bind="127.0.0.1:8080",
                 timeout=180,
                 reload_server=False):
        self.app = create_app()
        self.bind = bind
        self.timeout = timeout
        self.reload_server = reload_server
        super().__init__(None, None)

    def init(self, parser, opts, args):
        pass

    def load_config(self):
        s = self.cfg.set
        s("bind", self.bind)
        s("timeout", self.timeout)
        s("reload", self.reload_server)

    def load(self):
        return self.app


CONTEXT_SETTINGS = dict(help_option_names=["-h", "--help"])


@click.command(context_settings=CONTEXT_SETTINGS)
@click.option(
    "-b",
    "--bind",
    metavar="ADDRESS",
    type=str,
    default="127.0.0.1:8080",
    show_default=True,
    help="The socket to bind.",
)
@click.option(
    "-t",
    "--timeout",
    type=int,
    default=180,
    show_default=True,
    help=
    "Workers silent for more than this many seconds are killed and restarted.",
)
@click.option(
    "-r",
    "--reload",
    "reload_server",
    is_flag=True,
    default=False,
    show_default=True,
    help="Restart workers when code changes.",
)
@click.option(
    "-d",
    "--debug",
    is_flag=True,
    default=False,
    show_default=True,
    help="Increase logs verbosity.",
)
@click.version_option(__version__, "-v", "--version")
def main(bind, timeout, reload_server, debug):
    """Starts the production server"""
    if debug:
        config_logger(debug=True)

    app = StandaloneApplication(bind=bind,
                                timeout=timeout,
                                reload_server=reload_server)

    app.run()


if __name__ == "__main__":
    StandaloneApplication().run()
