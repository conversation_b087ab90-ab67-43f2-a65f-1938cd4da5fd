openapi: 3.0.3
info:
  title: Aerosint API
  description: The API of the Aerosint Scarlett test bench.
  version: 3.3.0
servers:
  - url: /api/v3
tags:
  - name: Debug
    description: Endpoints used for maintenance and debugging.
paths:
  /x:
    get:
      summary: Get X axis info.
      description: Returns information about the X axis.
      operationId: get_x_info
      responses:
        "200":
          description: X axis info successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - running
                  - position
                  - homed
                properties:
                  running:
                    type: boolean
                    description: A flag that indicates if the X axis moves.
                  position:
                    type: number
                    format: double
                    minimum: 0
                    description: "The X axis position [mm]."
                    example: 12.345
                  homed:
                    type: boolean
                    description: A flag that indicates if the X axis has been homed.
        "404":
          description: Not found
          content:
            application/json:
              schema:
                type: object
                description: HTTP status code and error message returned by the server.
                required:
                  - status_code
                  - message
                properties:
                  status_code:
                    type: integer
                    format: int32
                    example: 200
                  message:
                    type: string
                    example: Error message
  /x/motion:
    get:
      summary: Get the current motion command.
      description: Returns the current motion command if there is a motion. Returns nothing otherwise.
      operationId: get_x_motion
      responses:
        "200":
          description: Motion command successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/paths/~1x~1motion/post/requestBody/content/application~1json/schema"
        "204":
          description: There is currently no motion.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
    post:
      summary: Post a motion command.
      description: Creates a motion command if possible.
      operationId: set_x_motion
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mode
                - speed
              properties:
                mode:
                  type: string
                  enum:
                    - absolute
                    - relative
                    - homing
                  example: relative
                  description: |
                    Motion's mode:
                      * absolute - Absolute movement, goes to the specified position.
                      * relative - Relative movement, travels the given distance.
                      * homing - Homing movement, returns to its reference position.
                speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The speed of the motion [mm/s]."
                  example: 30
                distance:
                  type: number
                  format: double
                  description: "The absolute or relative distance of the motion [mm]."
                  example: 100
      responses:
        "201":
          description: Motion command created.
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/paths/~1x/get/responses/404/content/application~1json/schema"
        "404":
          $ref: "#/paths/~1x/get/responses/404"
        "409":
          description: Conflict
          content:
            application/json:
              schema:
                $ref: "#/paths/~1x/get/responses/404/content/application~1json/schema"
    delete:
      summary: Delete the current motion command.
      description: Cancels and removes the current motion command.
      operationId: cancel_x_motion
      responses:
        "204":
          description: Motion command cancelled.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /z:
    get:
      summary: Get Z axis info.
      description: Returns information about the Z axis.
      operationId: get_z_info
      responses:
        "200":
          description: Z axis info successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - running
                  - position
                  - homed
                properties:
                  running:
                    type: boolean
                    description: A flag that indicates if the Z axis moves.
                  position:
                    type: number
                    format: double
                    minimum: 0
                    description: "The Z axis position [mm]."
                    example: 12.345
                  homed:
                    type: boolean
                    description: A flag that indicates if the Z axis has been homed.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /z/motion:
    get:
      summary: Get the current motion command.
      description: Returns the current motion command if there is a motion. Returns nothing otherwise.
      operationId: get_z_motion
      responses:
        "200":
          description: Motion command successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/paths/~1z~1motion/post/requestBody/content/application~1json/schema"
        "204":
          description: There is currently no motion.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
    post:
      summary: Post a motion command.
      description: Creates a motion command if possible.
      operationId: set_z_motion
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mode
                - speed
              properties:
                mode:
                  type: string
                  enum:
                    - absolute
                    - relative
                    - homing
                  example: relative
                  description: |
                    Motion's mode:
                      * absolute - Absolute movement, goes to the specified position.
                      * relative - Relative movement, travels the given distance.
                      * homing - Homing movement, returns to its reference position.
                speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The speed of the motion [mm/s]."
                  example: 5
                distance:
                  type: number
                  format: double
                  description: "The absolute or relative distance of the motion [mm]."
                  example: 100
      responses:
        "201":
          description: Motion command created.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
        "404":
          $ref: "#/paths/~1x/get/responses/404"
        "409":
          $ref: "#/paths/~1x~1motion/post/responses/409"
    delete:
      summary: Delete the current motion command.
      description: Cancels and removes the current motion command.
      operationId: cancel_z_motion
      responses:
        "204":
          description: Motion command cancelled.
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /z/gripper:
    get:
      summary: Get the gripper state.
      description: Returns the current state of the gripper.
      operationId: get_gripper_state
      responses:
        "200":
          description: Gripper state successfully retrieved.
          content:
            application/json:
              schema:
                $ref: "#/paths/~1z~1gripper/put/requestBody/content/application~1json/schema"
    put:
      summary: Set the gripper state.
      description: Defines the state of the gripper.
      operationId: put_gripper_state
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - state
              properties:
                state:
                  type: boolean
                  description: The state of the gripper.
      responses:
        "204":
          description: Gripper state successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
  /print/parameters:
    get:
      summary: Get print's parameters.
      description: Returns the current parameters of the print.
      operationId: get_print_parameters
      responses:
        "200":
          description: Layer parameters successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - filling_id
                  - patterning_speed
                properties:
                  filling_id:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/filling_id"
                  patterning_speed:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/patterning_speed"
                  x_offset:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/x_offset"
                  max_x_offset:
                    type: number
                    format: double
                    description: "The maximum offset along the X axis [mm]."
                    example: 100
                    minimum: 0
                  travel_speed:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/travel_speed"
                  z_speed:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/z_speed"
                  z_offset:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/z_offset"
                  layer_thickness:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/layer_thickness"
                  collectors_delay:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/collectors_delay"
                  powder_saving:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/powder_saving"
                  layer_start:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/layer_start"
                  layer_end:
                    $ref: "#/paths/~1print~1parameters/put/requestBody/content/application~1json/schema/properties/layer_end"
    put:
      summary: Set layer's parameters.
      description: Defines the parameters of the current layer.
      operationId: put_print_parameters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - filling_id
                - patterning_speed
              properties:
                filling_id:
                  type: integer
                  format: int32
                  description: "The ID of the drum with the filling material powder. If there should not be any filling, set this index to -1."
                  example: 1
                patterning_speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The patterning speed [mm/s]."
                  example: 30
                x_offset:
                  type: number
                  format: double
                  description: "The offset along the X axis [mm]."
                  example: 0
                  minimum: 0
                travel_speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The travel speed [mm/s]."
                  example: 30
                z_speed:
                  type: number
                  format: double
                  minimum: 0
                  description: "The patterning speed [mm/s]."
                  example: 30
                z_offset:
                  type: number
                  format: double
                  description: "The offset along the Z axis [mm]."
                  example: 0
                  minimum: 0
                layer_thickness:
                  type: number
                  format: double
                  description: "The layer thickness [mm]."
                  example: 0
                  minimum: 0
                collectors_delay:
                  type: integer
                  format: int32
                  description: The number of layer before emptying the collectors.
                  example: 10
                powder_saving:
                  type: boolean
                  description: A flag indicating if the powder saving strategies are used or not.
                  default: true
                layer_start:
                  type: integer
                  format: int32
                  description: The first layer to print.
                  example: 1
                layer_end:
                  type: integer
                  format: int32
                  description: The last layer to print.
                  example: 5
      responses:
        "204":
          description: Layer parameters successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
  /print/job:
    post:
      summary: Post a print job.
      description: Creates a printing job if the server is ready to start. The recoater starts the printing procedure and waits for the synchro signal.
      operationId: start_print_job
      responses:
        "202":
          description: Print job successfully created.
        "409":
          $ref: "#/paths/~1x~1motion/post/responses/409"
    delete:
      summary: Delete the current print job.
      description: Cancels and removes the current printing job.
      operationId: cancel_print_job
      responses:
        "204":
          description: Print job successfully cancelled.
  /print/info:
    get:
      summary: Get print info.
      description: Returns information about the print job.
      operationId: get_print_info
      responses:
        "200":
          description: Print info successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - n_layers
                  - last_layer
                properties:
                  n_layers:
                    type: integer
                    format: int32
                    minimum: 0
                  last_layer:
                    type: integer
                    format: int32
                    nullable: true
                    minimum: 0
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  /preview:
    get:
      summary: Get specified layer preview.
      description: Returns a PNG image preview of the layer. The image is a representation of the powder allocation.
      operationId: get_preview
      parameters:
        - name: layer_id
          in: query
          description: The index of the layer to preview.
          schema:
            type: integer
            format: int32
            minimum: 0
            default: 0
      responses:
        "200":
          description: Layer preview retrieved.
          content:
            image/png:
              schema:
                type: string
                format: binary
        "404":
          $ref: "#/paths/~1x/get/responses/404"
  "/geometries/{drum_id}":
    put:
      summary: Set the drum full geometry.
      description: Defines the full geometry of the specified drum.
      operationId: put_drum_geometry
      parameters:
        - name: drum_id
          in: path
          description: The drum's ID.
          required: true
          schema:
            type: integer
            format: int32
            minimum: 0
      requestBody:
        required: true
        content:
          application/octet-stream:
            schema:
              type: string
              format: binary
      responses:
        "204":
          description: Drum full geometry successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
    delete:
      summary: Delete the drum geometry.
      description: Removes the full geometry of the specified drum.
      operationId: delete_drum_geometry
      parameters:
        - name: drum_id
          in: path
          description: The drum's ID.
          required: true
          schema:
            type: integer
            format: int32
            minimum: 0
      responses:
        "204":
          description: Drum geometry successfully deleted.
  /state:
    get:
      summary: Get the server's state.
      description: Returns the current state of the server.
      operationId: get_state
      responses:
        "200":
          description: Server state successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - state
                properties:
                  state:
                    type: string
                    enum:
                      - ready
                      - printing
                      - waiting_for_reset
                      - error
                    example: ready
                    description: |
                      Server's state:
                        * ready - The server is waiting requests.
                        * printing - The server is printing a layer.
                        * waiting_for_reset - The server needs the reset button to be pushed
                        * error - The server has errors.
  /light:
    get:
      summary: Get the light state.
      description: Returns the current state of the light.
      operationId: get_light
      responses:
        "200":
          description: Light state successfully retrieved.
          content:
            application/json:
              schema:
                type: object
                required:
                  - state
                properties:
                  state:
                    type: boolean
                    example: true
                    description: |
                      Light state:
                        * true - the light is turned on
                        * false - the light is turned off.
    put:
      summary: Set the light state.
      description: Returns the current state of the light.
      operationId: set_light
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - state
              properties:
                state:
                  type: boolean
                  description: |
                    Light state:
                      * true - the light is turned on
                      * false - the light is turned off.
      responses:
        "204":
          description: Light state successfully set.
        "400":
          $ref: "#/paths/~1x~1motion/post/responses/400"
